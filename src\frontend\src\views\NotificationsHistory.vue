<template>
  <div class="notifications-container">
    <!-- Header Section -->
    <div class="notifications-header">
      <div class="header-content">
        <div class="header-left">
          <div class="notification-icon">
            <i class="bi bi-bell"></i>
          </div>
          <div class="header-text">
            <h1 class="page-title">Trade Notifications</h1>
            <span class="notification-count">{{ notifications.length }} notifications</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Notifications Content -->
    <div class="notifications-content">
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <span>Loading notifications...</span>
      </div>
      
      <div v-else-if="error" class="error-state">
        <i class="bi bi-exclamation-triangle"></i>
        <span>{{ error }}</span>
      </div>
      
      <div v-else-if="notifications.length === 0" class="empty-state">
        <i class="bi bi-bell-slash"></i>
        <h3>No notifications yet</h3>
        <p>Trade notifications will appear here when they are generated.</p>
      </div>
      
      <!-- Use Original Component with All Information -->
      <TradeListNotifications v-else :trades="notifications" :key="notifications.length" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'
import TradeListNotifications from '../components/TradeListNotifications.vue'
import axios from 'axios'
import { TradeNotification } from '../../../utils/types'

export default defineComponent({
  name: 'NotificationsHistory',
  components: {
    TradeListNotifications
  },
  setup() {
    const notifications = ref<TradeNotification[]>([])
    const loading = ref(true)
    const error = ref<string | null>(null)

    const fetchNotifications = async () => {
      try {
        loading.value = true
        const response = await axios.get('/api/notifications')
        notifications.value = response.data
        console.log(notifications.value)
        console.log(notifications)
        console.log(response.data)
      } catch (err) {
        error.value = 'Erro ao carregar notificações'
        console.error('Error fetching notifications:', err)
      } finally {
        loading.value = false
      }
    }

    onMounted(() => {
      fetchNotifications()
    })

    return {
      notifications,
      loading,
      error
    }
  }
})
</script>

<style scoped>
/* Notifications Container */
.notifications-container {
  /* Padding and max-width handled by parent content-container */
  min-height: 80vh;
}

/* Header Section */
.notifications-header {
  margin-bottom: 40px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(79, 172, 254, 0.3);
}

.notification-icon i {
  font-size: 1.8rem;
  color: #ffffff;
}

.header-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.notification-count {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

/* Notifications Content */
.notifications-content {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  overflow: hidden;
  padding: 20px;
}

/* Loading, Error, Empty States */
.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 30px;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
}

.loading-state {
  gap: 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(79, 172, 254, 0.3);
  border-top: 3px solid #4facfe;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.error-state {
  gap: 15px;
}

.error-state i {
  font-size: 3rem;
  color: #ef4444;
}

.empty-state {
  gap: 15px;
}

.empty-state i {
  font-size: 4rem;
  color: rgba(255, 255, 255, 0.3);
}

.empty-state h3 {
  color: #ffffff;
  margin: 0;
  font-size: 1.5rem;
}

.empty-state p {
  color: rgba(255, 255, 255, 0.5);
  margin: 0;
  max-width: 400px;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .notifications-container {
    padding: 20px 15px;
  }
  
  .header-left {
    gap: 15px;
  }
  
  .notification-icon {
    width: 50px;
    height: 50px;
  }
  
  .notification-icon i {
    font-size: 1.5rem;
  }
  
  .page-title {
    font-size: 1.6rem;
  }
  
  .notification-item {
    padding: 16px 20px;
    gap: 12px;
  }
  
  .notification-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .notification-time {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .notifications-container {
    padding: 15px 10px;
  }
  
  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .notification-item {
    padding: 12px 15px;
  }
  
  .notification-status-icon {
    width: 36px;
    height: 36px;
  }
  
  .notification-status-icon i {
    font-size: 1rem;
  }
}
</style> 