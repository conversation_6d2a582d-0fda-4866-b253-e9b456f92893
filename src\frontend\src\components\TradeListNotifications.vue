<template>
  <div>
    <!-- Add Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
      <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
          <strong class="me-auto">Success</strong>
          <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
          Trade executed successfully!
        </div>
      </div>
    </div>
    <div v-if="trades.length === 0" class="text-center text-muted">
      No trade notifications received yet
    </div>
    <div v-else class="list-group">
      <div v-for="(trade, index) in trades" :key="index" class="list-group-item">
        <div class="d-flex justify-content-between align-items-start">
          <div>
            <div class="d-flex justify-content-between align-items-center mb-1">
              <h6 class="mb-0">{{ trade.symbol }}</h6>
              <small class="text-muted">{{ formatTimestamp(trade.timestamp) }}</small>
            </div>
            <div class="d-flex gap-3">
              <span class="badge" :class="trade.type === 'LONG' ? 'bg-success' : 'bg-danger'">
                {{ trade.type }}
              </span>
              <span>Entry: {{ trade.entry }}</span>
              <span>Stop: {{ trade.stop }}</span>
              <span>Interval: {{ trade.interval }}</span>
            </div>
            <div class="mt-2">
              <strong>Take Profits:</strong>
              <div class="d-flex flex-wrap gap-3">
                <span v-if="trade.takeProfits.tp1">TP1: {{ trade.takeProfits.tp1 }}</span>
                <span v-if="trade.takeProfits.tp2">TP2: {{ trade.takeProfits.tp2 }}</span>
                <span v-if="trade.takeProfits.tp3">TP3: {{ trade.takeProfits.tp3 }}</span>
                <span v-if="trade.takeProfits.tp4">TP4: {{ trade.takeProfits.tp4 }}</span>
                <span v-if="trade.takeProfits.tp5">TP5: {{ trade.takeProfits.tp5 }}</span>
                <span v-if="trade.takeProfits.tp6">TP6: {{ trade.takeProfits.tp6 }}</span>
              </div>
            </div>
            <div class="mt-2">
              <div class="d-flex gap-2 align-items-center">
                <span class="badge" :class="trade.validation.isValid ? 'bg-success' : 'bg-danger'">
                  {{ trade.validation.isValid ? 'Valid' : 'Invalid' }}
                </span>
                <span v-if="trade.isWarning" class="badge bg-warning text-dark">
                  ⚠️ Warning
                </span>
                <span class="badge" :class="trade.volume_required ? 'bg-danger' : 'bg-success'">
                  {{ trade.volume_required ? 'Volume Required' : 'Volume Optional' }}
                </span>
                <span class="badge" :class="trade.volume_adds_margin ? 'bg-success' : 'bg-secondary'">
                  {{ trade.volume_adds_margin ? 'Adds Margin' : 'No Extra Margin' }}
                </span>
                <span class="badge" :class="trade.manually_generated ? 'bg-info' : 'bg-secondary'">
                  {{ trade.manually_generated ? 'Manual' : 'Auto' }}
                </span>
                <small class="text-muted">{{ trade.validation.message }}</small>
              </div>
              <div class="mt-1">
                <small class="text-muted">Entry Analysis: {{ trade.validation.entryAnalysis.message }}</small>
              </div>
              <div class="mt-1">
                <small class="text-muted">
                  Volume: {{ trade.validation.volumeAnalysis.currentVolume.toFixed(2) }} 
                  (std: {{ trade.validation.volumeAnalysis.stdBar.toFixed(2) }})
                  <span class="ms-1" :class="{
                    'text-danger': trade.validation.volumeAnalysis.color === 'RED',
                    'text-warning': trade.validation.volumeAnalysis.color === 'ORANGE',
                    'text-info': trade.validation.volumeAnalysis.color === 'YELLOW',
                    'text-secondary': trade.validation.volumeAnalysis.color === 'WHITE',
                    'text-primary': trade.validation.volumeAnalysis.color === 'BLUE'
                  }">
                    [{{ trade.validation.volumeAnalysis.color }}]
                  </span>
                </small>
              </div>
              <div v-if="trade.setup_description" class="mt-2">
                <div class="card bg-light">
                  <div class="card-body p-2">
                    <h6 class="card-title mb-1">Setup Description</h6>
                    <small class="text-muted">{{ trade.setup_description }}</small>
                  </div>
                </div>
              </div>
            </div>

            <!-- Execution Results Section -->
            <div v-if="trade.executionResult" class="mt-2">
              <div class="card bg-light">
                <div class="card-body p-2">
                  <h6 class="card-title mb-2">Execution Results</h6>
                  <div class="d-flex flex-column gap-1">
                    <div class="d-flex justify-content-between">
                      <small>Leverage:</small>
                      <small class="fw-bold">{{ trade.executionResult.leverage }}x</small>
                    </div>
                    <div class="d-flex justify-content-between">
                      <small>Quantity:</small>
                      <small class="fw-bold">{{ trade.executionResult.quantity.toFixed(4) }}</small>
                    </div>
                    <div v-if="trade.volume_adds_margin && trade.executionResult.volumeMarginAdded" class="mt-2 pt-2 border-top">
                      <div class="d-flex justify-content-between">
                        <small>Volume Margin:</small>
                        <small class="fw-bold text-success">+{{ trade.executionResult.volumeMarginAdded.percentage }}%</small>
                      </div>
                      <div class="d-flex justify-content-between">
                        <small>Base Margin:</small>
                        <small class="fw-bold">{{ trade.executionResult.volumeMarginAdded.baseMargin.toFixed(2) }}</small>
                      </div>
                      <div class="d-flex justify-content-between">
                        <small>Total Margin:</small>
                        <small class="fw-bold">{{ trade.executionResult.volumeMarginAdded.totalMargin.toFixed(2) }}</small>
                      </div>
                    </div>
                    <div class="d-flex justify-content-between">
                      <small>Entry Order:</small>
                      <small class="text-truncate" style="max-width: 150px;">{{ trade.executionResult.entryOrderId }}</small>
                    </div>
                    <div class="d-flex justify-content-between">
                      <small>Stop Order:</small>
                      <small class="text-truncate" style="max-width: 150px;">{{ trade.executionResult.stopOrderId }}</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Execution Error Section -->
            <div v-if="trade.executionError" class="mt-2">
              <div class="alert alert-danger p-2 mb-0">
                <small class="d-flex align-items-center">
                  <i class="bi bi-exclamation-triangle-fill me-1"></i>
                  <span>Execution Error: {{ trade.executionError }}</span>
                </small>
              </div>
            </div>

            <div class="mt-2">
              <div class="d-flex gap-2">
                <a :href="trade.analysisUrl" target="_blank" class="btn btn-sm btn-outline-primary">
                  View Analysis
                </a>
                <template v-if="trade.isWarning">
                  <button 
                    @click="enterMarket(trade)" 
                    class="btn btn-sm btn-success"
                    :disabled="trade.isLoading"
                  >
                    <span v-if="trade.isLoading" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                    {{ trade.isLoading ? 'Entering...' : 'Enter Market' }}
                  </button>
                  <button 
                    @click="enterMarketWithTP1(trade)" 
                    class="btn btn-sm btn-warning"
                    :disabled="trade.isLoadingTP1"
                  >
                    <span v-if="trade.isLoadingTP1" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                    {{ trade.isLoadingTP1 ? 'Entering...' : 'Enter Market (TP1 Adjusted)' }}
                  </button>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from 'vue'
import { TradeNotification } from '../../../utils/types'
import axios from 'axios'
import { Toast } from 'bootstrap'

const props = defineProps<{
  trades: TradeNotification[]
}>()

// Add loading states to trades
const trades = ref(props.trades.map(trade => ({
  ...trade,
  isLoading: false,
  isLoadingTP1: false
})))

const formatTimestamp = (timestamp: string) => {
  const date = new Date(timestamp);
  return date.toLocaleString(undefined, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
};

const showSuccessToast = () => {
  const toastEl = document.getElementById('successToast')
  if (toastEl) {
    const toast = new Toast(toastEl)
    toast.show()
  }
}

const enterMarket = async (trade: TradeNotification) => {
  const tradeIndex = trades.value.findIndex(t => t === trade)
  if (tradeIndex === -1) return

  trades.value[tradeIndex].isLoading = true
  try {
    await axios.post('/api/trade/market', trade)
    showSuccessToast()
  } catch (error) {
    console.error('Error entering market:', error)
    alert('Failed to enter market. Please try again.')
  } finally {
    trades.value[tradeIndex].isLoading = false
  }
}

const enterMarketWithTP1 = async (trade: TradeNotification) => {
  const tradeIndex = trades.value.findIndex(t => t === trade)
  if (tradeIndex === -1) return

  trades.value[tradeIndex].isLoadingTP1 = true
  try {
    await axios.post('/api/trade/market/tp_adjusted', trade)
    showSuccessToast()
  } catch (error) {
    console.error('Error entering market with modified TP1:', error)
    alert('Failed to enter market with modified TP1. Please try again.')
  } finally {
    trades.value[tradeIndex].isLoadingTP1 = false
  }
}
</script>

<style scoped>
/* Modern Trade Notifications Styling */
.list-group {
  background: transparent;
  border: none;
}

.list-group-item {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 16px !important;
  margin-bottom: 16px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  padding: 20px !important;
  position: relative;
  overflow: hidden;
}

.list-group-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 2px 0 0 2px;
}

.list-group-item:hover {
  background: rgba(255, 255, 255, 0.08) !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

/* Header Section */
.mb-0 {
  color: #ffffff !important;
  font-weight: 700;
  font-size: 1.1rem;
  margin-bottom: 8px !important;
}

.text-muted {
  color: rgba(255, 255, 255, 0.6) !important;
  font-size: 0.85rem;
  font-weight: 500;
}

/* Badge Styling */
.badge {
  padding: 4px 12px !important;
  border-radius: 16px !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid transparent;
}

.bg-success {
  background: rgba(34, 197, 94, 0.2) !important;
  color: #22c55e !important;
  border-color: rgba(34, 197, 94, 0.3) !important;
}

.bg-danger {
  background: rgba(239, 68, 68, 0.2) !important;
  color: #ef4444 !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
}

.bg-warning {
  background: rgba(245, 158, 11, 0.2) !important;
  color: #f59e0b !important;
  border-color: rgba(245, 158, 11, 0.3) !important;
}

.bg-info {
  background: rgba(59, 130, 246, 0.2) !important;
  color: #3b82f6 !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
}

.bg-secondary {
  background: rgba(107, 114, 128, 0.2) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  border-color: rgba(107, 114, 128, 0.3) !important;
}

.bg-light {
  background: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

.text-dark {
  color: #1a1a1a !important;
}

/* Trade Info Section */
.d-flex.gap-3 {
  gap: 12px !important;
  margin: 12px 0;
  flex-wrap: wrap;
}

.d-flex.gap-3 > span:not(.badge) {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.85rem;
  font-weight: 500;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Take Profits Section */
.mt-2 strong {
  color: #ffffff;
  font-weight: 600;
  font-size: 0.9rem;
}

.d-flex.flex-wrap.gap-3 {
  gap: 8px !important;
  margin-top: 8px;
}

.d-flex.flex-wrap.gap-3 > span {
  background: rgba(79, 172, 254, 0.2) !important;
  color: #4facfe !important;
  border: 1px solid rgba(79, 172, 254, 0.3) !important;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

/* Validation Section */
.d-flex.gap-2.align-items-center {
  gap: 8px !important;
  margin: 12px 0;
  flex-wrap: wrap;
}

.d-flex.gap-2.align-items-center .text-muted {
  background: rgba(255, 255, 255, 0.03);
  padding: 4px 8px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  font-size: 0.8rem;
}

/* Volume Analysis */
.text-danger {
  color: #ef4444 !important;
  font-weight: 600;
}

.text-warning {
  color: #f59e0b !important;
  font-weight: 600;
}

.text-info {
  color: #3b82f6 !important;
  font-weight: 600;
}

.text-secondary {
  color: rgba(255, 255, 255, 0.6) !important;
}

.text-primary {
  color: #4facfe !important;
  font-weight: 600;
}

/* Card Components */
.card {
  background: rgba(255, 255, 255, 0.03) !important;
  border: 1px solid rgba(255, 255, 255, 0.08) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(5px);
  margin-top: 12px;
}

.card-body {
  padding: 12px !important;
}

.card-title {
  color: #ffffff !important;
  font-size: 0.85rem !important;
  font-weight: 600 !important;
  margin-bottom: 8px !important;
}

.card .text-muted {
  color: rgba(255, 255, 255, 0.7) !important;
  font-size: 0.8rem;
  line-height: 1.4;
}

/* Execution Results */
.d-flex.justify-content-between {
  margin: 4px 0;
}

.d-flex.justify-content-between small {
  font-size: 0.8rem;
}

.d-flex.justify-content-between small:first-child {
  color: rgba(255, 255, 255, 0.7);
}

.d-flex.justify-content-between small.fw-bold {
  color: #ffffff;
  font-weight: 600;
}

.text-success {
  color: #22c55e !important;
  font-weight: 600;
}

.border-top {
  border-color: rgba(255, 255, 255, 0.1) !important;
  margin-top: 8px !important;
  padding-top: 8px !important;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: rgba(255, 255, 255, 0.8) !important;
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
}

/* Alert Styles */
.alert {
  border-radius: 12px !important;
  border: 1px solid rgba(239, 68, 68, 0.3) !important;
  background: rgba(239, 68, 68, 0.1) !important;
  color: #ef4444 !important;
  padding: 12px !important;
  margin: 12px 0 !important;
}

.alert .bi-exclamation-triangle-fill {
  color: #ef4444;
  margin-right: 8px;
}

/* Button Styles */
.btn {
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  border: 1px solid transparent !important;
}

.btn-outline-primary {
  background: rgba(79, 172, 254, 0.1) !important;
  color: #4facfe !important;
  border-color: rgba(79, 172, 254, 0.3) !important;
}

.btn-outline-primary:hover {
  background: rgba(79, 172, 254, 0.2) !important;
  color: #4facfe !important;
  border-color: rgba(79, 172, 254, 0.5) !important;
  transform: translateY(-1px);
}

.btn-success {
  background: rgba(34, 197, 94, 0.9) !important;
  color: #ffffff !important;
  border-color: rgba(34, 197, 94, 0.3) !important;
}

.btn-success:hover {
  background: rgba(34, 197, 94, 1) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

.btn-warning {
  background: rgba(245, 158, 11, 0.9) !important;
  color: #ffffff !important;
  border-color: rgba(245, 158, 11, 0.3) !important;
}

.btn-warning:hover {
  background: rgba(245, 158, 11, 1) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.btn-sm {
  font-size: 0.8rem !important;
  padding: 6px 12px !important;
}

/* Spinner Animation */
.spinner-border-sm {
  width: 14px !important;
  height: 14px !important;
  border-width: 2px !important;
}

/* Gap Utilities */
.gap-2 {
  gap: 8px !important;
}

.gap-3 {
  gap: 12px !important;
}

/* Margin Utilities */
.mt-1 {
  margin-top: 6px !important;
}

.mt-2 {
  margin-top: 12px !important;
}

.mb-1 {
  margin-bottom: 6px !important;
}

.me-1 {
  margin-right: 6px !important;
}

.ms-1 {
  margin-left: 6px !important;
}

/* Toast Styling */
.toast-container {
  z-index: 1050;
}

.toast {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px);
  border-radius: 12px !important;
}

.toast-header {
  background: rgba(0, 0, 0, 0.2) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
}

.toast-body {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Empty State */
.text-center.text-muted {
  color: rgba(255, 255, 255, 0.6) !important;
  font-size: 1rem;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

/* Responsive Design */
@media (max-width: 768px) {
  .list-group-item {
    padding: 16px !important;
    margin-bottom: 12px;
  }
  
  .d-flex.gap-3 {
    gap: 8px !important;
  }
  
  .btn-sm {
    font-size: 0.75rem !important;
    padding: 4px 8px !important;
  }
  
  .card-body {
    padding: 8px !important;
  }
}

@media (max-width: 480px) {
  .d-flex.gap-3 {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .d-flex.flex-wrap.gap-3 {
    gap: 4px !important;
  }
  
  .badge {
    font-size: 0.7rem !important;
    padding: 2px 8px !important;
  }
}
</style> 