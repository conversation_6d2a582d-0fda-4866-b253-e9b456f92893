<template>
  <div class="trade-list-container">
    <!-- Toast Container -->
      <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="toast-header">
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
          <div class="toast-body">
            Trade executed successfully!
          </div>
        </div>
      </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
      <div class="stat-card total">
        <div class="stat-content">
          <div class="stat-header">
            <span class="stat-label">Total</span>
            <i class="bi bi-list-ul stat-icon"></i>
          </div>
          <div class="stat-value">{{ trades.length }} Trades</div>
        </div>
      </div>
  
      <div class="stat-card pass">
        <div class="stat-content">
          <div class="stat-header">
            <span class="stat-label">Pass</span>
            <i class="bi bi-check-circle stat-icon"></i>
          </div>
          <div class="stat-value">0 Pass</div>
        </div>
      </div>
            
      <div class="stat-card long">
        <div class="stat-content">
          <div class="stat-header">
            <span class="stat-label">Long</span>
            <i class="bi bi-arrow-up-right stat-icon"></i>
          </div>
          <div class="stat-value">{{ longCount }} LONG</div>
        </div>
      </div>
      
      <div class="stat-card short">
        <div class="stat-content">
          <div class="stat-header">
            <span class="stat-label">Short</span>
            <i class="bi bi-arrow-down-right stat-icon"></i>
          </div>
          <div class="stat-value">{{ shortCount }} SHORT</div>
        </div>
      </div>
    </div>
  
    <!-- Action Buttons -->
    <div class="action-section">
      <div class="action-buttons">
        <router-link to="/trade/new" class="btn-action primary">
          <i class="bi bi-plus-lg"></i>
          <span>Add New Trade</span>
        </router-link>
        <button @click="showStats = !showStats" class="btn-action secondary" :class="{ active: showStats }">
          <i class="bi" :class="showStats ? 'bi-bar-chart-fill' : 'bi-bar-chart'"></i>
          <span>{{ showStats ? 'Hide Statistics' : 'Show Statistics' }}</span>
        </button>
        <button @click="showMarketButtons = !showMarketButtons" class="btn-action secondary" :class="{ active: showMarketButtons }">
          <i class="bi bi-eye"></i>
          <span>Show Market Buttons</span>
        </button>
      </div>

      <!-- Time Filters - Always Visible -->
      <div class="time-filters-container">
        <!-- Main Filter Buttons -->
        <div class="main-filters">
          <button 
            @click="selectedTimeFilter = 'all'"
            class="filter-btn main-filter"
            :class="{ active: selectedTimeFilter === 'all' }"
          >
            <span class="filter-count">{{ trades.length }}</span>
            <span class="filter-label">All</span>
          </button>
          <button 
            @click="selectedTimeFilter = 'pass'"
            class="filter-btn main-filter pass"
            :class="{ active: selectedTimeFilter === 'pass' }"
          >
            <span class="filter-count">0</span>
            <span class="filter-label">Pass</span>
          </button>
          <button 
            @click="selectedTimeFilter = 'long'"
            class="filter-btn main-filter long"
            :class="{ active: selectedTimeFilter === 'long' }"
          >
            <span class="filter-count">{{ longCount }}</span>
            <span class="filter-label">Long</span>
          </button>
          <button 
            @click="selectedTimeFilter = 'short'"
            class="filter-btn main-filter short"
            :class="{ active: selectedTimeFilter === 'short' }"
          >
            <span class="filter-count">{{ shortCount }}</span>
            <span class="filter-label">Short</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Time Interval Filters -->
    <div v-show="showStats" class="interval-filters-section">
      <div class="interval-filters">
        <button 
          @click="selectedTimeFilter = '5m'"
          class="interval-filter-btn"
          :class="{ active: selectedTimeFilter === '5m' }"
        >
          <span class="interval-label">5m:</span>
          <span class="interval-count">{{ getFilteredTrades('5m').length }} Trades</span>
          <span class="interval-details">
            <span class="detail-item pass">{{ getIntervalStats('5m').pass || 0 }} Pass</span>
            <span class="detail-item long">{{ getIntervalStats('5m').long }} LONG</span>
            <span class="detail-item short">{{ getIntervalStats('5m').short }} SHORT</span>
          </span>
        </button>

        <button 
          @click="selectedTimeFilter = '15m'"
          class="interval-filter-btn"
          :class="{ active: selectedTimeFilter === '15m' }"
        >
          <span class="interval-label">15m:</span>
          <span class="interval-count">{{ getFilteredTrades('15m').length }} Trades</span>
          <span class="interval-details">
            <span class="detail-item pass">{{ getIntervalStats('15m').pass || 0 }} Pass</span>
            <span class="detail-item long">{{ getIntervalStats('15m').long }} LONG</span>
            <span class="detail-item short">{{ getIntervalStats('15m').short }} SHORT</span>
          </span>
        </button>

        <button 
          @click="selectedTimeFilter = '1h'"
          class="interval-filter-btn"
          :class="{ active: selectedTimeFilter === '1h' }"
        >
          <span class="interval-label">1h:</span>
          <span class="interval-count">{{ getFilteredTrades('1h').length }} Trades</span>
          <span class="interval-details">
            <span class="detail-item">All Trades</span>
            <span class="detail-item pass">{{ getIntervalStats('1h').pass || 0 }} Pass</span>
            <span class="detail-item long">{{ getIntervalStats('1h').long }} LONG</span>
            <span class="detail-item short">{{ getIntervalStats('1h').short }} SHORT</span>
          </span>
        </button>
      </div>
    </div>



    <!-- Trades Table -->
    <div class="table-section">
      <div class="table-container">
        <table class="trades-table">
          <thead>
            <tr>
              <th>Pair & Setup</th>
              <th>Type & Interval</th>
              <th>Entry</th>
              <th>Stop</th>
              <th>Take Profits</th>
              <th>Volume Flags</th>
              <th>Analysis</th>
              <th>Actions</th>
              </tr>
            </thead>
            <tbody>
            <tr v-for="(trade, index) in paginatedTrades" :key="getTradeIndex(index)" class="trade-row">
              <td class="pair-cell">
                <div class="pair-info">
                  <div class="symbol">{{ trade.symbol }}</div>
                  <div v-if="trade.setup_description" class="setup-desc">{{ trade.setup_description }}</div>
                  </div>
                </td>
              
              <td class="type-cell">
                <div class="type-info">
                  <span class="type-badge" :class="{ long: trade.type === 'LONG', short: trade.type === 'SHORT' }">
                      {{ trade.type }}
                    </span>
                  <span class="interval-badge">{{ trade.interval || '-' }}</span>
                  </div>
                </td>
              
              <td class="entry-cell">{{ trade.entry }}</td>
              <td class="stop-cell">{{ trade.stop }}</td>
              
              <td class="tp-cell">
                <div class="tp-list">
                    <template v-for="(tp, tpIndex) in getFormattedTPs(trade)" :key="tpIndex">
                    <span class="tp-badge">{{ tp.label }}: {{ tp.value }}</span>
                    </template>
                  <span v-if="getFormattedTPs(trade).length === 0" class="no-tp">-</span>
                  </div>
                </td>
              
              <td class="volume-cell">
                <div class="volume-flags">
                  <span class="volume-badge" :class="{ required: trade.volume_required }">
                      {{ trade.volume_required ? 'Volume Required' : 'Volume Optional' }}
                    </span>
                  <span class="margin-badge" :class="{ active: trade.volume_adds_margin }">
                    {{ trade.volume_adds_margin ? 'No Volume Margin' : 'No Volume Margin' }}
                    </span>
                  </div>
                </td>
              
              <td class="analysis-cell">
                <a v-if="trade.url_analysis" :href="trade.url_analysis" target="_blank" class="analysis-btn">
                  <i class="bi bi-eye"></i>
                </a>
                <span v-else class="no-analysis">-</span>
                </td>
              
              <td class="actions-cell">
                <div class="action-buttons-group">
                  <button @click="deleteTrade(getTradeIndex(index))" class="action-btn delete" title="Delete">
                    <i class="bi bi-trash"></i>
                    </button>
                  <router-link :to="`/trade/${getTradeIndex(index)}/edit`" class="action-btn edit" title="Edit">
                    <i class="bi bi-pencil"></i>
                  </router-link>
                    <template v-if="showMarketButtons">
                    <button @click="enterMarket(trade)" class="action-btn enter" :disabled="trade.isLoading" title="Enter Market">
                      <span v-if="trade.isLoading" class="spinner"></span>
                      <i v-else class="bi bi-play"></i>
                      </button>
                    <button @click="enterMarketWithTP1(trade)" class="action-btn enter-tp1" :disabled="trade.isLoadingTP1" title="Enter TP1">
                      <span v-if="trade.isLoadingTP1" class="spinner"></span>
                      <i v-else class="bi bi-skip-forward"></i>
                      </button>
                    </template>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

    <!-- Pagination -->
    <div v-if="totalPages > 1" class="pagination-section">
      <div class="pagination-info">
        Showing {{ startIndex + 1 }} to {{ endIndex }} of {{ filteredTrades.length }} trades
        </div>
      <div class="pagination-controls">
        <button @click="goToPage(currentPage - 1)" :disabled="currentPage === 1" class="page-btn prev">
          <i class="bi bi-chevron-left"></i>
              </button>
        
        <template v-for="page in visiblePages" :key="page">
          <button @click="goToPage(page)" class="page-btn" :class="{ active: page === currentPage }">
            {{ page }}
          </button>
        </template>
        
        <button @click="goToPage(currentPage + 1)" :disabled="currentPage === totalPages" class="page-btn next">
          <i class="bi bi-chevron-right"></i>
              </button>
      </div>
      </div>

      <TradeNotifications />    
  </div>
  </template>
  
  <script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
  import TradeNotifications from '../components/TradeNotifications.vue'
  import { Trade } from '../../../utils/types';
  import { Toast } from 'bootstrap'
  import axios from 'axios'
  
  const trades = ref<Trade[]>([])

// Time filters
const selectedTimeFilter = ref('all')
  
  // Pagination variables
  const currentPage = ref(1)
  const itemsPerPage = ref(10)

// Filter functions
const getFilteredTrades = (filter: string) => {
  switch (filter) {
    case '5m':
      return trades.value.filter(trade => trade.interval === '5m')
    case '15m':
      return trades.value.filter(trade => trade.interval === '15m')
    case '1h':
      return trades.value.filter(trade => trade.interval === '1h')
    case 'long':
      return trades.value.filter(trade => trade.type === 'LONG')
    case 'short':
      return trades.value.filter(trade => trade.type === 'SHORT')
    case 'pass':
      return [] // No pass logic implemented yet
    case 'all':
    default:
      return trades.value
  }
}

const filteredTrades = computed(() => {
  return getFilteredTrades(selectedTimeFilter.value)
})
  
  // Pagination computed properties
  const totalPages = computed(() => {
  return Math.ceil(filteredTrades.value.length / itemsPerPage.value)
  })
  
  const startIndex = computed(() => {
    return (currentPage.value - 1) * itemsPerPage.value
  })
  
  const endIndex = computed(() => {
    const end = currentPage.value * itemsPerPage.value
  return Math.min(end, filteredTrades.value.length)
  })
  
  const paginatedTrades = computed(() => {
    // Reverse the trades array to show most recent first
  const reversedTrades = [...filteredTrades.value].reverse()
    return reversedTrades.slice(startIndex.value, endIndex.value)
  })
  
  const visiblePages = computed(() => {
    const pages = []
    const start = Math.max(1, currentPage.value - 2)
    const end = Math.min(totalPages.value, currentPage.value + 2)
    
    for (let i = start; i <= end; i++) {
      pages.push(i)
    }
    
    return pages
  })
  
  const uniquePairs = computed(() => {
    const pairs = new Set(trades.value.map(trade => trade.symbol))
    return Array.from(pairs)
  })
  
  const longCount = computed(() => {
    return trades.value.filter(trade => trade.type === 'LONG').length
  })
  
  const shortCount = computed(() => {
    return trades.value.filter(trade => trade.type === 'SHORT').length
  })
  
  // Helper function to get formatted TPs for a trade
  const getFormattedTPs = (trade: Trade) => {
    const tps = []
    for (let i = 1; i <= 6; i++) {
      const tp = trade[`tp${i}` as keyof Trade] as number | null
      if (tp !== null && tp !== undefined) {
        tps.push({ label: `TP${i}`, value: tp })
      }
    }
    return tps
  }
  
  // Load trades from API
  const loadTrades = async () => {
    try {
      const response = await fetch('/api/trades')
      const loadedTrades = await response.json()
      trades.value = loadedTrades.map((trade: Trade) => ({
        ...trade,
        isLoading: false,
        isLoadingTP1: false
      }))
      
      // Reset to first page if current page doesn't exist
      const newTotalPages = Math.ceil(trades.value.length / itemsPerPage.value)
      if (currentPage.value > newTotalPages && newTotalPages > 0) {
        currentPage.value = 1
      }
    } catch (error) {
      console.error('Failed to load trades:', error)
    }
  }
  
  // Delete trade
  const deleteTrade = async (index: number) => {
    if (!confirm('Are you sure you want to delete this trade?')) return
    
    try {
      await fetch(`/api/trades/${index}`, { method: 'DELETE' })
      await loadTrades()
    } catch (error) {
      console.error('Failed to delete trade:', error)
    }
  }
  
  const getIntervalStats = (interval: string) => {
    const intervalTrades = trades.value.filter(trade => trade.interval === interval)
    const pairs = new Set(intervalTrades.map(trade => trade.symbol))
    
    return {
      total: intervalTrades.length,
      pairs: pairs.size,
      long: intervalTrades.filter(trade => trade.type === 'LONG').length,
    short: intervalTrades.filter(trade => trade.type === 'SHORT').length,
    pass: 0 // No pass logic implemented yet
    }
  }
  
const showStats = ref(false)
  const showMarketButtons = ref(false)
  
  const showSuccessToast = () => {
    const toastEl = document.getElementById('successToast')
    if (toastEl) {
      const toast = new Toast(toastEl)
      toast.show()
    }
  }
  
  const enterMarket = async (trade: Trade) => {
    const tradeIndex = trades.value.findIndex(t => t === trade)
    if (tradeIndex === -1) return

    trades.value[tradeIndex].isLoading = true
    
    // Transform trade into TradeNotification format
    const tradeNotification = {
      symbol: trade.symbol,
      type: trade.type,
      entry: trade.entry,
      stop: trade.stop,
      takeProfits: {
        tp1: trade.tp1,
        tp2: trade.tp2,
        tp3: trade.tp3,
        tp4: trade.tp4,
        tp5: trade.tp5,
        tp6: trade.tp6
      },
      validation: {
        isValid: true,
        message: 'Trade forced by user',
        volumeAnalysis: {
          color: 'green',
          stdBar: 0,
          currentVolume: 0,
          mean: 0,
          std: 0
        },
        entryAnalysis: {
          currentClose: trade.entry,
          canEnter: true,
          hasClosePriceBeforeEntry: true,
          message: 'Trade forced by user'
        }
      },
      analysisUrl: trade.url_analysis || '',
      volume_adds_margin: trade.volume_adds_margin,
      setup_description: trade.setup_description,
      volume_required: trade.volume_required,
      interval: trade.interval,
      timestamp: new Date().toISOString()
    }
    console.log(tradeNotification)
    try {
      await axios.post('/api/trade/market', tradeNotification)
      showSuccessToast()
    } catch (error) {
      console.error('Error entering market:', error)
      alert('Failed to enter market. Please try again.')
    } finally {
      trades.value[tradeIndex].isLoading = false
    }
  }
  
  const enterMarketWithTP1 = async (trade: Trade) => {
    const tradeIndex = trades.value.findIndex(t => t === trade)
    if (tradeIndex === -1) return

    trades.value[tradeIndex].isLoadingTP1 = true
    try {
      
      // Transform trade into TradeNotification format
      const tradeNotification = {
        symbol: trade.symbol,
        type: trade.type,
        entry: trade.entry,
        stop: trade.stop,
        takeProfits: {
          tp1: trade.tp1,
          tp2: trade.tp2,
          tp3: trade.tp3,
          tp4: trade.tp4,
          tp5: trade.tp5,
          tp6: trade.tp6
        },
        validation: {
          isValid: true,
          message: 'Trade forced by user (TP1 adjusted)',
          volumeAnalysis: {
            color: 'green',
            stdBar: 0,
            currentVolume: 0,
            mean: 0,
            std: 0
          },
          entryAnalysis: {
            currentClose: trade.entry,
            canEnter: true,
            hasClosePriceBeforeEntry: true,
            message: 'Trade forced by user (TP1 adjusted)'
          }
        },
        analysisUrl: trade.url_analysis || '',
        volume_adds_margin: trade.volume_adds_margin,
        setup_description: trade.setup_description,
        volume_required: trade.volume_required,
        interval: trade.interval,
        timestamp: new Date().toISOString()
      }
      console.log(tradeNotification)
      await axios.post('/api/trade/market/tp_adjusted', tradeNotification)
      showSuccessToast()
    } catch (error) {
      console.error('Error entering market with modified TP1:', error)
      alert('Failed to enter market with modified TP1. Please try again.')
    } finally {
      trades.value[tradeIndex].isLoadingTP1 = false
    }
  }
  
  const scrollToSymbol = (symbol: string) => {
    const el = document.getElementById(`symbol-${symbol}`)
    if (el) {
      el.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }
  
  // Pagination functions
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page
    }
  }
  
  const getTradeIndex = (paginatedIndex: number) => {
    // Since we reversed the array, we need to calculate the original index
    const reversedIndex = startIndex.value + paginatedIndex
  const filteredTradeItem = [...filteredTrades.value].reverse()[reversedIndex]
  return trades.value.findIndex(trade => 
    trade.symbol === filteredTradeItem.symbol && 
    trade.entry === filteredTradeItem.entry &&
    trade.stop === filteredTradeItem.stop
  )
}

// Watch for filter changes to reset pagination
watch(selectedTimeFilter, () => {
  currentPage.value = 1
})
  
  // Initialize
  onMounted(() => {
    loadTrades()
  })
  </script>

<style scoped>
/* Trade List Container */
.trade-list-container {
  /* Padding and max-width handled by parent content-container */
}

/* Statistics Cards Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 30px;
}

.stat-card {
  background: rgba(26, 37, 59, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 16px 20px;
  transition: all 0.6s ease;
  position: relative;
  height: auto;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  backdrop-filter: blur(10px);
}



.stat-card.total {
  border-color: rgba(100, 116, 139, 0.2);
  background: rgba(30, 35, 50, 0.63);
}

.stat-card.pass {
  border-color: rgba(34, 197, 94, 0.25);
  background: rgba(16, 45, 30, 0.66);
}

.stat-card.long {
  border-color: rgba(59, 131, 246, 0.32);
  background: rgba(16, 35, 65, 0.4);
}

.stat-card.short {
  border-color: rgba(239, 68, 68, 0.2);
  background: rgba(50, 20, 25, 0.62);
}

.stat-card.total:hover {
  border-color: rgba(100, 116, 139, 0.4);
  background: rgba(35, 45, 65, 0.8);
}

.stat-card.pass:hover {
  border-color: rgba(34, 197, 94, 0.5);
  background: rgba(20, 55, 35, 0.8);
}

.stat-card.long:hover {
  border-color: rgba(59, 131, 246, 0.5);
  background: rgba(20, 45, 85, 0.6);
}

.stat-card.short:hover {
  border-color: rgba(239, 68, 68, 0.4);
  background: rgba(65, 30, 40, 0.8);
}

.stat-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: stretch;
  gap: 8px;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin: 0;
}

.stat-label {
  font-size: 0.75rem;
  font-weight: 400;
  line-height: 1;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 1;
}

.stat-card.total .stat-label {
  color: #94a3b8;
}

.stat-card.pass .stat-label {
  color: #94a3b8;
}

.stat-card.long .stat-label {
  color: #94a3b8;
}

.stat-card.short .stat-label {
  color: #94a3b8;
}

.stat-icon {
  font-size: 1.1rem !important;
  opacity: 1 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 28px !important;
  height: 28px !important;
  background: transparent !important;
  border-radius: 6px !important;
  font-family: 'bootstrap-icons' !important;
  font-style: normal !important;
  font-weight: normal !important;
  line-height: 1 !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.stat-card.total .stat-icon {
  color: rgba(255, 255, 255, 0.6) !important;
}

.stat-card.pass .stat-icon {
  color: #22c55e !important;
}

.stat-card.long .stat-icon {
  color: #3b82f6 !important;
}

.stat-card.short .stat-icon {
  color: #ef4444 !important;
}

.stat-value {
  font-size: 1.375rem;
  font-weight: 600;
  line-height: 1.2;
  margin: 0;
  text-align: left;
  align-self: stretch;
}

.stat-card.total .stat-value {
  color: #ffffff;
}

.stat-card.pass .stat-value {
  color: #22c55e;
}

.stat-card.long .stat-value {
  color: #3b82f6;
}

.stat-card.short .stat-value {
  color: #ef4444;
}

/* Action Section */
.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  gap: 20px;
  flex-wrap: wrap;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.btn-action {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 10px;
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.btn-action.primary {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.btn-action.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
  color: #ffffff;
}

.btn-action.secondary {
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-action.secondary:hover,
.btn-action.secondary.active {
  background: rgba(79, 172, 254, 0.1);
  color: #4facfe;
  border-color: rgba(79, 172, 254, 0.3);
}

/* Main Filters Container */
.time-filters-container {
  display: flex;
  align-items: center;
}

.main-filters {
  display: flex;
  gap: 8px;
}

.filter-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 60px;
}

.filter-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.filter-btn.active {
  background: rgba(79, 172, 254, 0.2);
  border-color: rgba(79, 172, 254, 0.4);
  color: #4facfe;
}

.filter-btn.pass.active {
  background: rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.4);
  color: #22c55e;
}

.filter-btn.long.active {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
  color: #3b82f6;
}

.filter-btn.short.active {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.4);
  color: #ef4444;
}

.filter-count {
  font-size: 1.1rem;
  font-weight: 700;
  line-height: 1;
}

.filter-label {
  font-size: 0.75rem;
  opacity: 0.8;
  margin-top: 2px;
}

/* Interval Filters Section */
.interval-filters-section {
  margin-bottom: 30px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  padding: 15px 20px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  animation: slideInStats 0.3s ease-out;
}

@keyframes slideInStats {
  from {
    opacity: 0;
    transform: translateY(-10px);
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
    margin-bottom: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 200px;
    padding-top: 15px;
    padding-bottom: 15px;
    margin-bottom: 30px;
  }
}

.interval-filters {
  display: flex;
  gap: 0;
  align-items: center;
  flex-wrap: wrap;
}

.interval-filter-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 6px;
  font-size: 0.85rem;
}

.interval-filter-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
}

.interval-filter-btn.active {
  background: rgba(79, 172, 254, 0.1);
  color: #4facfe;
}

.interval-label {
  font-weight: 600;
  min-width: 30px;
}

.interval-count {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  margin-right: 8px;
}

.interval-details {
  display: flex;
  gap: 8px;
  align-items: center;
}

.detail-item {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.7);
}

.detail-item.pass {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.detail-item.long {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.detail-item.short {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}



/* Table Section */
.table-section {
  margin-bottom: 30px;
}

.table-container {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.trades-table {
  width: 100%;
  border-collapse: collapse;
}

.trades-table thead th {
  background: rgba(0, 0, 0, 0.3);
  color: rgba(255, 255, 255, 0.9);
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.trades-table tbody .trade-row {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.trades-table tbody .trade-row:hover {
  background: rgba(255, 255, 255, 0.02);
}

.trades-table td {
  padding: 16px 12px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  vertical-align: top;
}

/* Table Cell Styles */
.pair-info .symbol {
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4px;
}

.pair-info .setup-desc {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  max-width: 200px;
  word-wrap: break-word;
}

.type-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.type-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}

.type-badge.long {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.type-badge.short {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.interval-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

.tp-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tp-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  background: rgba(79, 172, 254, 0.2);
  color: #4facfe;
  border: 1px solid rgba(79, 172, 254, 0.3);
}

.no-tp {
  color: rgba(255, 255, 255, 0.4);
}

.volume-flags {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.volume-badge, .margin-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  text-align: center;
}

.volume-badge {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
}

.volume-badge.required {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.margin-badge {
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.5);
}

.margin-badge.active {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.analysis-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: rgba(79, 172, 254, 0.1);
  color: #4facfe;
  text-decoration: none;
  transition: all 0.3s ease;
}

.analysis-btn:hover {
  background: rgba(79, 172, 254, 0.2);
  color: #4facfe;
  transform: scale(1.1);
}

.no-analysis {
  color: rgba(255, 255, 255, 0.4);
}

/* Action Buttons Group */
.action-buttons-group {
  display: flex;
  gap: 4px;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  font-size: 0.8rem;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn.delete {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.action-btn.delete:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.1);
}

.action-btn.edit {
  background: rgba(79, 172, 254, 0.1);
  color: #4facfe;
}

.action-btn.edit:hover {
  background: rgba(79, 172, 254, 0.2);
  color: #4facfe;
  transform: scale(1.1);
}

.action-btn.enter {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.action-btn.enter:hover {
  background: rgba(34, 197, 94, 0.2);
  transform: scale(1.1);
}

.action-btn.enter-tp1 {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.action-btn.enter-tp1:hover {
  background: rgba(245, 158, 11, 0.2);
  transform: scale(1.1);
}

.spinner {
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Pagination */
.pagination-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
}

.pagination-info {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
}

.pagination-controls {
  display: flex;
  gap: 4px;
}

.page-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.page-btn:hover:not(:disabled) {
  background: rgba(79, 172, 254, 0.1);
  color: #4facfe;
  border-color: rgba(79, 172, 254, 0.3);
}

.page-btn.active {
  background: rgba(79, 172, 254, 0.2);
  color: #4facfe;
  border-color: rgba(79, 172, 254, 0.4);
}

.page-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Toast */
.toast-container {
  z-index: 1050;
}

.toast {
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .trade-list-container {
    padding: 20px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .trade-list-container {
    padding: 15px;
  }
  
  .action-section {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .time-filters-container {
    justify-content: center;
  }
  
  .main-filters {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .interval-filters {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .interval-filter-btn {
    justify-content: flex-start;
    text-align: left;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .trades-table {
    font-size: 0.8rem;
  }
  
  .trades-table td {
    padding: 12px 8px;
  }
  
  .pagination-section {
    flex-direction: column;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .table-container {
    overflow-x: auto;
  }
  
  .trades-table {
    min-width: 800px;
  }
  
  .action-buttons {
    justify-content: center;
  }
  
  .filter-btn {
    min-width: 50px;
    padding: 6px 12px;
  }
  
  .filter-count {
    font-size: 1rem;
  }
  
  .filter-label {
    font-size: 0.7rem;
  }
  
  .interval-details {
    flex-wrap: wrap;
  }
}
</style>