import TelegramBot from 'node-telegram-bot-api';
import { TradeNotification } from './utils/types';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export class TelegramService {
    private readonly bot?: TelegramBot;
    private readonly chatId: string;
    private static instance: TelegramService;

    private constructor() {
        const telegramToken = process.env.TELEGRAM_BOT_TOKEN;
        const telegramChatId = process.env.TELEGRAM_CHAT_ID;

        if (!telegramToken || !telegramChatId) {
            console.warn('Telegram configuration not found in .env file. Telegram notifications will be disabled.');
            console.warn('Please set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID in your .env file.');
            this.chatId = '';
        } else {
            this.chatId = telegramChatId;
            this.bot = new TelegramBot(telegramToken, { polling: false });
            console.log('Telegram service initialized successfully');
        }
    }

    public static getInstance(): TelegramService {
        if (!TelegramService.instance) {
            TelegramService.instance = new TelegramService();
        }
        return TelegramService.instance;
    }

    public isConfigured(): boolean {
        return !!this.bot && !!this.chatId;
    }

    private async sendMessage(message: string): Promise<void> {
        if (!this.isConfigured()) {
            console.warn('Telegram service is not properly configured. Skipping notification.');
            return;
        }

        try {
            await this.bot!.sendMessage(this.chatId, message, { parse_mode: 'HTML' });
            console.log('Telegram notification sent successfully');
        } catch (error) {
            console.error('Error sending Telegram notification:', error);
            throw error;
        }
    }

    private formatTradeMessage(trade: TradeNotification): string {
        const direction = trade.type === 'LONG' ? '🟢 LONG' : '🔴 SHORT';
        const status = trade.isWarning ? '⚠️ AVISO' : '✅ TRADE';
        const validationStatus = trade.validation.isValid ? '✅ Válido' : '❌ Inválido';
        
        // Formatação dos Take Profits - somente os que existem, em linha
        const takeProfits = [];
        if (trade.takeProfits.tp1) takeProfits.push(`TP1: ${trade.takeProfits.tp1}`);
        if (trade.takeProfits.tp2) takeProfits.push(`TP2: ${trade.takeProfits.tp2}`);
        if (trade.takeProfits.tp3) takeProfits.push(`TP3: ${trade.takeProfits.tp3}`);
        if (trade.takeProfits.tp4) takeProfits.push(`TP4: ${trade.takeProfits.tp4}`);
        if (trade.takeProfits.tp5) takeProfits.push(`TP5: ${trade.takeProfits.tp5}`);
        if (trade.takeProfits.tp6) takeProfits.push(`TP6: ${trade.takeProfits.tp6}`);
        
        const takeProfitsFormatted = takeProfits.length > 0 
            ? `   ${takeProfits.join(' | ')}` 
            : `   TP1: ${trade.takeProfits.tp1}`;

        // Seção principal do trade
        let message = `<b>🚨 ${status} - ${direction}</b>
📊 Par: ${trade.symbol}
💰 Entrada: ${trade.entry}
🛑 Stop: ${trade.stop}
🎯 Take Profits: ${takeProfitsFormatted.trim()}

⏰ Intervalo: ${trade.interval || '1h'}
🕒 Timestamp: ${new Date(trade.timestamp).toLocaleString()}
📊 Status: ${validationStatus}`;

        // Seções condicionais organizadas
        const additionalSections = [];
        
        if (trade.validation.message) {
            additionalSections.push(`📝 Mensagem: ${trade.validation.message}`);
        }
        
        if (trade.setup_description) {
            additionalSections.push(`📋 Setup: ${trade.setup_description}`);
        }
        
        if (trade.executionError) {
            additionalSections.push(`❌ Erro: ${trade.executionError}`);
        }
        
        if (trade.executionResult) {
            let executionInfo = `✅ Trade Executado:
   Alavancagem: ${trade.executionResult.leverage}x
   Quantidade: ${trade.executionResult.quantity}`;
            
            if (trade.executionResult.volumeMarginAdded) {
                executionInfo += `
   Margem Adicional: ${trade.executionResult.volumeMarginAdded.percentage}%
   Margem Base: ${trade.executionResult.volumeMarginAdded.baseMargin.toFixed(2)}
   Margem Total: ${trade.executionResult.volumeMarginAdded.totalMargin.toFixed(2)}`;
            }
            
            additionalSections.push(executionInfo);
        }
        
        if (trade.analysisUrl) {
            additionalSections.push(`🔍 <a href="${trade.analysisUrl}">Ver Análise</a>`);
        }

        // Adiciona seções condicionais se existirem
        if (additionalSections.length > 0) {
            message += '\n\n' + additionalSections.join('\n\n');
        }

        return message;
    }

    public async sendTradeNotification(trade: TradeNotification): Promise<void> {
        const message = this.formatTradeMessage(trade);
        await this.sendMessage(message);
    }

    public async sendCustomMessage(message: string): Promise<void> {
        await this.sendMessage(message);
    }
} 