<template>
  <div class="min-vh-100 trade-manager-app">
    <!-- Modern Minimalist Header -->
    <header class="trade-header">
      <div class="header-container">
        <div class="header-grid">
          <!-- Logo Section -->
          <div class="brand-section">
            <h1 class="brand-title">Trade Manager</h1>
          </div>
          
          <!-- Navigation Section -->
          <nav class="main-navigation">
            <router-link to="/" class="nav-item" active-class="active">
              <i class="bi bi-list-ul"></i>
              <span class="nav-text">Trades</span>
            </router-link>
            <router-link to="/dashboard" class="nav-item" active-class="active">
              <i class="bi bi-graph-up"></i>
              <span class="nav-text">Dashboard</span>
            </router-link>
            <router-link to="/notifications" class="nav-item" active-class="active">
              <i class="bi bi-bell"></i>
              <span class="nav-text">Notificações</span>
            </router-link>
          </nav>
          
          <!-- Status Section -->
          <div class="status-section">
            <div class="connection-status" :class="connectionStatusClass">
              <div class="status-indicator"></div>
              <span class="status-text">{{ connectionStatus }}</span>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
      <div class="content-container">
        <router-view v-slot="{ Component }">
          <component :is="Component" />
        </router-view>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, provide } from 'vue'
import { TradeNotification } from '../../utils/types'

// WebSocket state
const connectionStatus = ref('Connecting...')
const connectionStatusClass = ref('warning')
const trades = ref<TradeNotification[]>([])
let ws: WebSocket | null = null

const connectWebSocket = () => {
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
  const wsUrl = `${protocol}//localhost:3000`

  ws = new WebSocket(wsUrl)

  ws.onopen = () => {
    connectionStatus.value = 'Connected'
    connectionStatusClass.value = 'connected'
  }

  ws.onclose = () => {
    connectionStatus.value = 'Disconnected'
    connectionStatusClass.value = 'disconnected'
    // Try to reconnect after 30 seconds
    setTimeout(connectWebSocket, 30000)
  }

  ws.onerror = (error) => {
    console.error('WebSocket error:', error)
    connectionStatus.value = 'Error'
    connectionStatusClass.value = 'disconnected'
  }

  ws.onmessage = (event) => {
    try {
      console.log('Received trade notification:', event.data)
      const trade: TradeNotification = JSON.parse(event.data)
      trades.value.unshift(trade) // Add new trade at the beginning

      // Keep only the last 50 trades
      if (trades.value.length > 50) {
        trades.value = trades.value.slice(0, 50)
      }
    } catch (error) {
      console.error('Error parsing trade notification:', error)
    }
  }
}

// Provide trades data to child components
provide('trades', trades)

// Initialize dark mode and WebSocket
onMounted(() => {
  document.documentElement.setAttribute('data-bs-theme', 'dark')
  connectWebSocket()
})

onUnmounted(() => {
  if (ws) {
    ws.close()
  }
})
</script>

<style>
/* Trade Manager App Styles */
.trade-manager-app {
  background: linear-gradient(135deg, #0a0e1a 0%, #1a1f35 50%, #2a2f45 100%);
  min-height: 100vh;
}

/* Modern Minimalist Header */
.trade-header {
  background: rgba(255, 255, 255, 0.02);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

.header-grid {
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  gap: 32px;
  height: 64px;
}

/* Brand Section */
.brand-section {
  display: flex;
  align-items: center;
}

.brand-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Navigation Styles */
.main-navigation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  color: rgba(255, 255, 255, 0.65);
  text-decoration: none;
  border-radius: 12px;
  transition: all 0.2s ease;
  font-weight: 500;
  font-size: 0.9rem;
  position: relative;
  min-width: 120px;
  justify-content: center;
}

.nav-item i {
  font-size: 1rem;
  opacity: 0.8;
}

.nav-item .nav-text {
  transition: all 0.2s ease;
}

.nav-item:hover {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.06);
  transform: translateY(-1px);
}

.nav-item:hover i {
  opacity: 1;
}

.nav-item.active {
  color: #4facfe;
  background: rgba(79, 172, 254, 0.08);
  border: 1px solid rgba(79, 172, 254, 0.2);
}

.nav-item.active i {
  opacity: 1;
}

/* Status Section */
.status-section {
  display: flex;
  justify-content: flex-end;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.status-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.connection-status.connected {
  background: rgba(34, 197, 94, 0.08);
  color: #22c55e;
  border-color: rgba(34, 197, 94, 0.15);
}

.connection-status.connected .status-indicator {
  background: #22c55e;
}

.connection-status.disconnected {
  background: rgba(239, 68, 68, 0.08);
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.15);
}

.connection-status.disconnected .status-indicator {
  background: #ef4444;
}

.connection-status.warning {
  background: rgba(245, 158, 11, 0.08);
  color: #f59e0b;
  border-color: rgba(245, 158, 11, 0.15);
}

.connection-status.warning .status-indicator {
  background: #f59e0b;
}

/* Main Content */
.main-content {
  flex: 1;
}

.content-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
}

/* Status animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Dark theme compatibility */
[data-bs-theme="dark"] {
  --bs-body-bg: #0a0e1a;
  --bs-body-color: #f8f9fa;
}

[data-bs-theme="dark"] .card {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .table {
  color: #f8f9fa;
}

[data-bs-theme="dark"] .table-light {
  background-color: rgba(255, 255, 255, 0.05);
  color: #f8f9fa;
}

[data-bs-theme="dark"] .table-hover tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: #f8f9fa;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .header-container {
    padding: 0 16px;
  }
  
  .content-container {
    padding: 16px;
  }
  
  .header-grid {
    gap: 24px;
  }
}

@media (max-width: 768px) {
  .header-grid {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "brand"
      "nav"
      "status";
    height: auto;
    padding: 16px 0;
    gap: 16px;
    text-align: center;
  }
  
  .brand-section {
    grid-area: brand;
    justify-content: center;
  }
  
  .main-navigation {
    grid-area: nav;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .status-section {
    grid-area: status;
    justify-content: center;
  }
  
  .nav-item {
    padding: 8px 12px;
    font-size: 0.85rem;
    min-width: 100px;
  }
  
  .brand-title {
    font-size: 1.125rem;
  }
  
  .header-container {
    padding: 0 12px;
  }
  
  .content-container {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .main-navigation {
    flex-direction: column;
    width: 100%;
    gap: 4px;
  }
  
  .nav-item {
    width: 100%;
    max-width: 200px;
    padding: 10px 16px;
  }
  
  .header-grid {
    gap: 12px;
  }
}
</style> 