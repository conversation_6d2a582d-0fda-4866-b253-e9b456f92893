<template>
  <div class="dashboard bg-dark text-light min-vh-100">
    <!-- Enhanced Header -->
    <div class="dashboard-header">
      <div class="container-fluid">
        <!-- Breadcrumb Navigation -->
        <nav aria-label="breadcrumb" class="mb-3">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <i class="bi bi-house-door me-1"></i>
              <span class="text-muted">Home</span>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
              <i class="bi bi-graph-up me-1"></i>
              Trading Dashboard
            </li>
          </ol>
        </nav>

        <!-- Main Header -->
        <div class="header-content">
          <div class="row align-items-center">
            <div class="col-lg-8">
              <div class="header-title-section">
                <div class="d-flex align-items-center mb-2">
                  <div class="header-icon">
                    <i class="bi bi-speedometer2"></i>
                  </div>
                  <div>
                    <h1 class="dashboard-title mb-0">Trading Dashboard</h1>
                    <p class="dashboard-subtitle mb-0">Complete analysis of position history</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-lg-4">
              <div class="header-actions text-lg-end">
                <div class="d-flex gap-2 justify-content-lg-end">
                  <button class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-download me-1"></i>
                    Export
                  </button>
                  <button class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-gear me-1"></i>
                    Settings
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

      <!-- Enhanced Loading State -->
      <div v-if="loading" class="loading-container">
        <div class="loading-content">
          <div class="loading-spinner">
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
          </div>
          <div class="loading-text">
            <h4 class="loading-title">Loading Dashboard</h4>
            <p class="loading-subtitle">Fetching your trading data...</p>
          </div>
          <div class="loading-progress">
            <div class="progress-bar"></div>
          </div>
        </div>
      </div>

      <div v-else>
        <!-- Enhanced Filters Section -->
        <div class="filters-section mb-4">
          <div class="container-fluid">
            <div class="filters-card">
              <div class="filters-header">
                <div class="d-flex align-items-center">
                  <div class="filter-icon">
                    <i class="bi bi-funnel"></i>
                  </div>
                  <div>
                    <h5 class="filters-title mb-0">Filters & Controls</h5>
                    <p class="filters-subtitle mb-0">Customize your data view</p>
                  </div>
                </div>
              </div>

              <div class="filters-body">
                <div class="row g-3">
                  <!-- Filter Controls Group -->
                  <div class="col-lg-8">
                    <div class="filter-controls-grid">
                      <div class="filter-group">
                        <label for="symbolFilter" class="filter-label">
                          <i class="bi bi-currency-exchange me-1"></i>
                          Symbol
                        </label>
                        <select v-model="filters.symbol" @change="loadData" class="form-select filter-select" id="symbolFilter">
                          <option value="ALL">All Symbols</option>
                          <option v-for="symbol in availableSymbols" :key="symbol" :value="symbol">
                            {{ symbol }}
                          </option>
                        </select>
                      </div>

                      <div class="filter-group">
                        <label for="setupFilter" class="filter-label">
                          <i class="bi bi-gear me-1"></i>
                          Setup
                        </label>
                        <select v-model="filters.setupDescription" @change="loadData" class="form-select filter-select" id="setupFilter">
                          <option value="ALL">All Setups</option>
                          <option v-for="setup in availableSetupDescriptions" :key="setup" :value="setup">
                            {{ setup }}
                          </option>
                        </select>
                      </div>

                      <div class="filter-group">
                        <label for="startDate" class="filter-label">
                          <i class="bi bi-calendar-event me-1"></i>
                          Start Date
                        </label>
                        <input
                          type="date"
                          v-model="filters.startDate"
                          @change="loadData"
                          class="form-control filter-input"
                          id="startDate"
                        >
                      </div>

                      <div class="filter-group">
                        <label for="endDate" class="filter-label">
                          <i class="bi bi-calendar-check me-1"></i>
                          End Date
                        </label>
                        <input
                          type="date"
                          v-model="filters.endDate"
                          @change="loadData"
                          class="form-control filter-input"
                          id="endDate"
                        >
                      </div>
                    </div>
                  </div>

                  <!-- Action Buttons Group -->
                  <div class="col-lg-4">
                    <div class="filter-actions">
                      <label class="filter-label d-block mb-2">Actions</label>
                      <div class="action-buttons">
                        <button @click="loadData" class="btn btn-primary action-btn">
                          <i class="bi bi-arrow-clockwise me-2"></i>
                          Refresh Data
                        </button>
                        <button @click="resetFilters" class="btn btn-outline-light action-btn">
                          <i class="bi bi-x-circle me-2"></i>
                          Clear Filters
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Enhanced Statistics Cards -->
        <div class="stats-section mb-5">
          <div class="container-fluid">
            <div class="stats-grid">
              <!-- Total Positions Card -->
              <div class="stat-card stat-card-primary">
                <div class="stat-card-content">
                  <div class="stat-icon">
                    <i class="bi bi-graph-up-arrow"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">Total Positions</div>
                    <div class="stat-value">{{ stats.totalPositions }}</div>
                    <div class="stat-trend">
                      <i class="bi bi-arrow-up"></i>
                      <span>Active trades</span>
                    </div>
                  </div>
                </div>
                <div class="stat-card-bg"></div>
              </div>

              <!-- Total Profit Card -->
              <div class="stat-card stat-card-success">
                <div class="stat-card-content">
                  <div class="stat-icon">
                    <i class="bi bi-currency-dollar"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">Total Profit</div>
                    <div class="stat-value">${{ formatNumber(stats.totalProfit) }}</div>
                    <div class="stat-trend">
                      <i class="bi bi-arrow-up"></i>
                      <span>Gains</span>
                    </div>
                  </div>
                </div>
                <div class="stat-card-bg"></div>
              </div>

              <!-- Total Loss Card -->
              <div class="stat-card stat-card-danger">
                <div class="stat-card-content">
                  <div class="stat-icon">
                    <i class="bi bi-graph-down-arrow"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">Total Loss</div>
                    <div class="stat-value">${{ formatNumber(stats.totalLoss) }}</div>
                    <div class="stat-trend">
                      <i class="bi bi-arrow-down"></i>
                      <span>Losses</span>
                    </div>
                  </div>
                </div>
                <div class="stat-card-bg"></div>
              </div>

              <!-- Net Result Card -->
              <div class="stat-card" :class="stats.netProfit >= 0 ? 'stat-card-success' : 'stat-card-danger'">
                <div class="stat-card-content">
                  <div class="stat-icon">
                    <i class="bi bi-calculator"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">Net Result</div>
                    <div class="stat-value">${{ formatNumber(stats.netProfit) }}</div>
                    <div class="stat-trend">
                      <i :class="stats.netProfit >= 0 ? 'bi bi-arrow-up' : 'bi bi-arrow-down'"></i>
                      <span>{{ stats.netProfit >= 0 ? 'Profit' : 'Loss' }}</span>
                    </div>
                  </div>
                </div>
                <div class="stat-card-bg"></div>
              </div>

              <!-- Win Rate Card -->
              <div class="stat-card stat-card-info">
                <div class="stat-card-content">
                  <div class="stat-icon">
                    <i class="bi bi-bullseye"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">Win Rate</div>
                    <div class="stat-value">{{ stats.winRate }}%</div>
                    <div class="stat-trend">
                      <i class="bi bi-target"></i>
                      <span>Success rate</span>
                    </div>
                  </div>
                </div>
                <div class="stat-card-bg"></div>
              </div>

              <!-- Sharpe Ratio Card -->
              <div class="stat-card stat-card-warning">
                <div class="stat-card-content">
                  <div class="stat-icon">
                    <i class="bi bi-bar-chart-line"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">Sharpe Ratio</div>
                    <div class="stat-value">{{ formatNumber(detailedStats?.performanceMetrics?.sharpeRatio || 0) }}</div>
                    <div class="stat-trend">
                      <i class="bi bi-graph-up"></i>
                      <span>Risk-adjusted</span>
                    </div>
                  </div>
                </div>
                <div class="stat-card-bg"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Enhanced Analysis Cards Section -->
        <div class="analysis-section mb-5">
          <div class="container-fluid">
            <div class="section-header mb-4">
              <h3 class="section-title">
                <i class="bi bi-graph-up me-2"></i>
                Risk & Performance Analysis
              </h3>
              <p class="section-subtitle">Detailed insights into your trading performance</p>
            </div>

            <div class="analysis-grid">
              <!-- Risk Stats Card -->
              <div class="analysis-card">
                <RiskStatsCard :stats="riskStats" />
              </div>

              <!-- Performance Metrics Card -->
              <div class="analysis-card">
                <PerformanceMetricsCard :stats="performanceStats" />
              </div>

              <!-- Enhanced Trade Metrics Card -->
              <div class="analysis-card">
                <div class="enhanced-card">
                  <div class="enhanced-card-header">
                    <div class="card-header-content">
                      <div class="card-icon">
                        <i class="bi bi-gear"></i>
                      </div>
                      <div>
                        <h5 class="card-title">Trade Metrics</h5>
                        <p class="card-subtitle">Trading execution details</p>
                      </div>
                    </div>
                  </div>

                  <div class="enhanced-card-body">
                    <!-- Metrics Grid -->
                    <div class="metrics-grid">
                      <div class="metric-item">
                        <div class="metric-value text-primary">{{ formatNumber(stats.tradeMetrics?.avgLeverage || 0) }}x</div>
                        <div class="metric-label">Avg Leverage</div>
                      </div>
                      <div class="metric-item">
                        <div class="metric-value text-info">${{ formatNumber(stats.tradeMetrics?.avgEntryPrice || 0) }}</div>
                        <div class="metric-label">Avg Entry</div>
                      </div>
                      <div class="metric-item">
                        <div class="metric-value text-warning">${{ formatNumber(stats.tradeMetrics?.avgStopPrice || 0) }}</div>
                        <div class="metric-label">Avg Stop</div>
                      </div>
                      <div class="metric-item">
                        <div class="metric-value text-success">${{ formatNumber(stats.tradeMetrics?.avgTakeProfit1 || 0) }}</div>
                        <div class="metric-label">Avg TP1</div>
                      </div>
                    </div>

                    <!-- Trade Highlights -->
                    <div class="trade-highlights">
                      <div class="highlight-item">
                        <span class="highlight-label">Best Trade</span>
                        <span class="highlight-badge badge-success">${{ formatNumber(stats.tradeMetrics?.bestProfit || 0) }}</span>
                      </div>
                      <div class="highlight-item">
                        <span class="highlight-label">Worst Trade</span>
                        <span class="highlight-badge badge-danger">${{ formatNumber(stats.tradeMetrics?.worstProfit || 0) }}</span>
                      </div>
                      <div class="highlight-item">
                        <span class="highlight-label">Top Symbol</span>
                        <span class="highlight-badge badge-primary">{{ stats.tradeMetrics?.mostProfitableSymbol || 'N/A' }}</span>
                      </div>
                      <div class="highlight-item">
                        <span class="highlight-label">Top Side</span>
                        <span class="highlight-badge badge-secondary">{{ stats.tradeMetrics?.mostProfitableSide || 'N/A' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Detailed Statistics -->
        <div class="row mb-4">
          <div class="col-lg-6 mb-3">
            <div class="card border-0 shadow h-100">
              <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0 fw-semibold">Performance Statistics</h5>
              </div>
              <div class="card-body">
                <div class="row g-3">
                  <div class="col-6">
                    <div class="text-center">
                      <div class="h3 text-success fw-bold">{{ stats.winRate }}%</div>
                      <div class="text-muted small">Win Rate</div>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="text-center">
                      <div class="h3 text-primary fw-bold">{{ stats.totalPositions }}</div>
                      <div class="text-muted small">Total Trades</div>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="text-center">
                      <div class="h3 text-success fw-bold">${{ formatNumber(stats.avgProfit) }}</div>
                      <div class="text-muted small">Average Profit</div>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="text-center">
                      <div class="h3 text-danger fw-bold">${{ formatNumber(stats.avgLoss) }}</div>
                      <div class="text-muted small">Average Loss</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-6 mb-3">
            <div class="card border-0 shadow h-100">
              <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0 fw-semibold">Extremes</h5>
              </div>
              <div class="card-body">
                <div class="row g-3">
                  <div class="col-6">
                    <div class="text-center">
                      <div class="h3 text-success fw-bold">${{ formatNumber(stats.maxProfit) }}</div>
                      <div class="text-muted small">Highest Profit</div>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="text-center">
                      <div class="h3 text-danger fw-bold">${{ formatNumber(stats.maxLoss) }}</div>
                      <div class="text-muted small">Highest Loss</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Enhanced Analysis Tables Section -->
        <div class="tables-section mb-5">
          <div class="container-fluid">
            <div class="section-header mb-4">
              <h3 class="section-title">
                <i class="bi bi-table me-2"></i>
                Detailed Analysis
              </h3>
              <p class="section-subtitle">Comprehensive breakdown by symbol, side, and setup</p>
            </div>

            <div class="tables-grid">
              <div class="table-card">
                <AnalysisTable
                  title="Symbol Analysis"
                  columnTitle="Symbol"
                  :data="detailedStats?.symbolAnalysis || {}"
                />
              </div>
              <div class="table-card">
                <AnalysisTable
                  title="Side Analysis"
                  columnTitle="Side"
                  :data="detailedStats?.sideAnalysis || {}"
                />
              </div>
              <div class="table-card">
                <SetupAnalysisCard :positions="positions" />
              </div>
            </div>
          </div>
        </div>

        <!-- Enhanced Charts Section -->
        <div class="charts-section mb-5">
          <div class="container-fluid">
            <div class="section-header mb-4">
              <h3 class="section-title">
                <i class="bi bi-bar-chart-line me-2"></i>
                Performance Charts
              </h3>
              <p class="section-subtitle">Visual analysis of your trading performance</p>
            </div>

            <!-- Main Performance Chart -->
            <div class="chart-row mb-4">
              <div class="chart-card chart-card-large">
                <div class="chart-header">
                  <div class="chart-header-content">
                    <div class="chart-icon chart-icon-success">
                      <i class="bi bi-graph-up"></i>
                    </div>
                    <div>
                      <h5 class="chart-title">Cumulative Profit Over Time</h5>
                      <p class="chart-subtitle">Track your profit progression</p>
                    </div>
                  </div>
                  <div class="chart-actions">
                    <button class="btn btn-sm btn-outline-light">
                      <i class="bi bi-fullscreen"></i>
                    </button>
                  </div>
                </div>
                <div class="chart-body">
                  <PerformanceChart :positions="positions" />
                </div>
              </div>
            </div>

            <!-- Secondary Charts Grid -->
            <div class="charts-grid mb-4">
              <!-- Profit by Symbol -->
              <div class="chart-card">
                <div class="chart-header">
                  <div class="chart-header-content">
                    <div class="chart-icon chart-icon-success">
                      <i class="bi bi-pie-chart"></i>
                    </div>
                    <div>
                      <h5 class="chart-title">Profit by Symbol</h5>
                      <p class="chart-subtitle">Symbol performance breakdown</p>
                    </div>
                  </div>
                </div>
                <div class="chart-body">
                  <ProfitBySymbolChart :positions="positions" />
                </div>
              </div>

              <!-- Loss by Symbol -->
              <div class="chart-card">
                <div class="chart-header">
                  <div class="chart-header-content">
                    <div class="chart-icon chart-icon-danger">
                      <i class="bi bi-pie-chart"></i>
                    </div>
                    <div>
                      <h5 class="chart-title">Loss by Symbol</h5>
                      <p class="chart-subtitle">Loss distribution analysis</p>
                    </div>
                  </div>
                </div>
                <div class="chart-body">
                  <LossBySymbolChart :positions="positions" />
                </div>
              </div>

              <!-- Monthly Performance -->
              <div class="chart-card chart-card-wide">
                <div class="chart-header">
                  <div class="chart-header-content">
                    <div class="chart-icon chart-icon-warning">
                      <i class="bi bi-bar-chart"></i>
                    </div>
                    <div>
                      <h5 class="chart-title">Monthly Performance</h5>
                      <p class="chart-subtitle">Month-over-month analysis</p>
                    </div>
                  </div>
                </div>
                <div class="chart-body">
                  <MonthlyPerformanceChart :positions="positions" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Costs Panel -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="card border-0 shadow">
              <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0 fw-semibold">
                  <i class="bi bi-calculator me-2"></i>
                  Trading Costs Analysis
                </h5>
              </div>
              <div class="card-body">
                <div class="row g-4">
                  <!-- Commission Summary -->
                  <div class="col-lg-4">
                    <div class="text-center p-3 border-end border-secondary">
                      <div class="h2 text-warning fw-bold mb-2">${{ formatNumber(totalCommission) }}</div>
                      <div class="text-muted mb-2">Total Commission</div>
                      <div class="small text-muted">
                        Avg: ${{ formatNumber(averageCommission) }} per trade
                      </div>
                    </div>
                  </div>
                  
                  <!-- Funding Summary -->
                  <div class="col-lg-4">
                    <div class="text-center p-3 border-end border-secondary">
                      <div class="h2 text-info fw-bold mb-2">${{ formatNumber(totalFunding) }}</div>
                      <div class="text-muted mb-2">Total Funding</div>
                      <div class="small text-muted">
                        Avg: ${{ formatNumber(averageFunding) }} per trade
                      </div>
                    </div>
                  </div>
                  
                  <!-- Total Costs -->
                  <div class="col-lg-4">
                    <div class="text-center p-3">
                      <div class="h2 text-danger fw-bold mb-2">${{ formatNumber(totalCosts) }}</div>
                      <div class="text-muted mb-2">Total Trading Costs</div>
                      <div class="small text-muted">
                        {{ ((totalCosts / stats.netProfit) * 100).toFixed(1) }}% of net profit
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Detailed Breakdown -->
                <div class="row mt-4">
                  <div class="col-12">
                    <h6 class="text-muted mb-3">Cost Breakdown by Symbol</h6>
                    <div class="table-responsive">
                      <table class="table table-sm table-borderless">
                        <thead class="table-dark">
                          <tr>
                            <th>Symbol</th>
                            <th class="text-end">Commission</th>
                            <th class="text-end">Funding</th>
                            <th class="text-end">Total Costs</th>
                            <th class="text-end">% of Net Profit</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(costs, symbol) in costsBySymbol" :key="symbol">
                            <td>
                              <span class="badge bg-secondary">{{ symbol }}</span>
                            </td>
                            <td class="text-end text-warning">${{ formatNumber(costs.commission) }}</td>
                            <td class="text-end text-info">${{ formatNumber(costs.funding) }}</td>
                            <td class="text-end text-danger fw-bold">${{ formatNumber(costs.total) }}</td>
                            <td class="text-end text-muted small">
                              {{ costs.percentageOfProfit }}%
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Positions Table -->
        <div class="row">
          <div class="col-12">
            <div class="card border-0 shadow">
              <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0 fw-semibold">Position History</h5>
                <div class="d-flex align-items-center">
                  <span class="text-white-50 small">Showing {{ positions.length }} positions</span>
                </div>
              </div>
              <div class="card-body p-0">
                <div class="table-responsive">
                  <table class="table table-hover mb-0">
                    <thead class="table-dark">
                      <tr>
                        <th>Symbol</th>
                        <th>Type</th>
                        <th>Quantity</th>
                        <th>Average Price</th>
                        <th>Close Price</th>
                        <th>Leverage</th>
                        <th>Result</th>
                        <th>Costs</th>
                        <th>Setup</th>
                        <th>Trade Info</th>
                        <th>Open Date</th>
                        <th>Close Date</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="position in positions" :key="position.positionId">
                        <td>
                          <span class="badge bg-primary">{{ position.symbol }}</span>
                        </td>
                        <td>
                          <span 
                            class="badge" 
                            :class="position.positionSide === 'LONG' ? 'bg-success' : 'bg-danger'"
                          >
                            {{ position.positionSide }}
                          </span>
                        </td>
                        <td>{{ position.closePositionAmt }}</td>
                        <td>${{ formatNumber(parseFloat(position.avgPrice), 5) }}</td>
                        <td>${{ formatNumber(position.avgClosePrice, 5) }}</td>
                        <td>{{ position.leverage }}x</td>
                        <td>
                          <span 
                            class="fw-bold" 
                            :class="parseFloat(position.netProfit) >= 0 ? 'text-success' : 'text-danger'"
                          >
                            ${{ formatNumber(parseFloat(position.netProfit)) }}
                          </span>
                        </td>
                        <td>
                          <div class="small">
                            <div class="text-warning">${{ formatNumber(parseFloat(position.positionCommission)) }}</div>
                            <div class="text-info">${{ formatNumber(parseFloat(position.totalFunding)) }}</div>
                          </div>
                        </td>
                        <td>
                          <div v-if="position.tradeInfo?.found && position.tradeInfo.trade?.setup_description" class="small">
                            <span class="badge bg-info">{{ position.tradeInfo.trade.setup_description }}</span>
                          </div>
                          <div v-else class="text-muted small">-</div>
                        </td>
                        <td>
                          <div v-if="position.tradeInfo?.found" class="small">
                            <div class="text-success">Trade #{{ position.tradeInfo.trade?.id }}</div>
                            <div class="text-muted">Entry: ${{ formatNumber(position.tradeInfo.trade?.entry || 0, 5) }}</div>
                            <div class="text-muted">Stop: ${{ formatNumber(position.tradeInfo.trade?.stop || 0, 5) }}</div>
                          </div>
                          <div v-else class="text-muted small">No trade info</div>
                        </td>
                        <td>{{ formatDate(position.openTime) }}</td>
                        <td>{{ formatDate(getEffectiveCloseTime(position)) }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { PositionHistory, PositionStats, DetailedRiskStats } from '../types/positionHistory'
import PerformanceChart from '../components/PerformanceChart.vue'
import ProfitBySymbolChart from '../components/ProfitBySymbolChart.vue'
import LossBySymbolChart from '../components/LossBySymbolChart.vue'
import MonthlyPerformanceChart from '../components/MonthlyPerformanceChart.vue'
import RiskStatsCard from '../components/RiskStatsCard.vue'
import PerformanceMetricsCard from '../components/PerformanceMetricsCard.vue'
import AnalysisTable from '../components/AnalysisTable.vue'
import SetupAnalysisCard from '../components/SetupAnalysisCard.vue'

// Types
interface CostsBySymbol {
  commission: number
  funding: number
  total: number
  percentageOfProfit: string
}

/**
 * Helper function to get the effective close time, using updateTime as fallback
 */
const getEffectiveCloseTime = (position: PositionHistory): number => {
  return position.closeTime || position.updateTime
}

// Reactive data
const loading = ref(false)
const positions = ref<PositionHistory[]>([])
const stats = ref<PositionStats>({
  totalPositions: 0,
  totalProfit: 0,
  totalLoss: 0,
  netProfit: 0,
  winRate: 0,
  avgProfit: 0,
  avgLoss: 0,
  maxProfit: 0,
  maxLoss: 0,
  profitBySymbol: {},
  profitBySide: {}
})
const detailedStats = ref<DetailedRiskStats | null>(null)
const availableSymbols = ref<string[]>([])
const availableSetupDescriptions = ref<string[]>([])
const filters = ref({
  symbol: 'ALL',
  setupDescription: 'ALL',
  startDate: '',
  endDate: ''
})

// Computed properties for risk stats
const riskStats = computed(() => {
  if (!detailedStats.value) {
    return {
      avgRiskPerTrade: 0,
      avgRiskRewardRatio: 0,
      sharpeRatio: 0,
      maxDrawdown: 0,
      tradesWithPositiveRR: 0,
      tradesWithNegativeRR: 0,
      bestRiskRewardRatio: 0,
      worstRiskRewardRatio: 0
    }
  }
  
  return {
    avgRiskPerTrade: detailedStats.value.riskAnalysis.avgRiskPerTrade,
    avgRiskRewardRatio: detailedStats.value.riskRewardAnalysis.avgRiskRewardRatio,
    sharpeRatio: detailedStats.value.performanceMetrics.sharpeRatio,
    maxDrawdown: detailedStats.value.performanceMetrics.maxDrawdown,
    tradesWithPositiveRR: detailedStats.value.riskRewardAnalysis.tradesWithPositiveRR,
    tradesWithNegativeRR: detailedStats.value.riskRewardAnalysis.tradesWithNegativeRR,
    bestRiskRewardRatio: detailedStats.value.riskRewardAnalysis.bestRiskRewardRatio,
    worstRiskRewardRatio: detailedStats.value.riskRewardAnalysis.worstRiskRewardRatio
  }
})

const performanceStats = computed(() => {
  if (!detailedStats.value) {
    return {
      sharpeRatio: 0,
      sortinoRatio: 0,
      calmarRatio: 0,
      recoveryFactor: 0,
      maxDrawdown: 0,
      avgDrawdown: 0
    }
  }
  
  return {
    sharpeRatio: detailedStats.value.performanceMetrics.sharpeRatio,
    sortinoRatio: detailedStats.value.performanceMetrics.sortinoRatio,
    calmarRatio: detailedStats.value.performanceMetrics.calmarRatio,
    recoveryFactor: detailedStats.value.performanceMetrics.recoveryFactor,
    maxDrawdown: detailedStats.value.performanceMetrics.maxDrawdown,
    avgDrawdown: detailedStats.value.performanceMetrics.avgDrawdown
  }
})

// Computed properties for costs
const totalCommission = computed(() => {
  return positions.value.reduce((total, position) => {
    return total + parseFloat(position.positionCommission || '0')
  }, 0)
})

const totalFunding = computed(() => {
  return positions.value.reduce((total, position) => {
    return total + parseFloat(position.totalFunding || '0')
  }, 0)
})

const totalCosts = computed(() => {
  return totalCommission.value + totalFunding.value
})

const averageCommission = computed(() => {
  return positions.value.length > 0 ? totalCommission.value / positions.value.length : 0
})

const averageFunding = computed(() => {
  return positions.value.length > 0 ? totalFunding.value / positions.value.length : 0
})

const costsBySymbol = computed(() => {
  const costs: { [key: string]: CostsBySymbol } = {}
  
  positions.value.forEach(position => {
    const symbol = position.symbol
    const commission = parseFloat(position.positionCommission || '0')
    const funding = parseFloat(position.totalFunding || '0')
    const netProfit = parseFloat(position.netProfit || '0')
    
    if (!costs[symbol]) {
      costs[symbol] = {
        commission: 0,
        funding: 0,
        total: 0,
        percentageOfProfit: '0.0'
      }
    }
    
    costs[symbol].commission += commission
    costs[symbol].funding += funding
    costs[symbol].total += commission + funding
    
    // Calculate percentage of net profit
    if (netProfit !== 0) {
      const percentage = ((commission + funding) / Math.abs(netProfit)) * 100
      costs[symbol].percentageOfProfit = percentage.toFixed(1)
    }
  })
  
  return costs
})

// Methods
const loadAvailableSymbols = async () => {
  try {
    const response = await fetch('/api/position-history/symbols')
    const result = await response.json()
    if (result.success) {
      availableSymbols.value = result.data
    }
  } catch (error) {
    console.error('Error loading symbols:', error)
  }
}

const loadAvailableSetupDescriptions = async () => {
  try {
    const response = await fetch('/api/position-history/setup-descriptions')
    const result = await response.json()
    if (result.success) {
      availableSetupDescriptions.value = result.data
    }
  } catch (error) {
    console.error('Error loading setup descriptions:', error)
  }
}

const loadData = async () => {
  loading.value = true
  try {
    // Load available symbols if not loaded yet
    if (availableSymbols.value.length === 0) {
      await loadAvailableSymbols()
    }

    // Load available setup descriptions if not loaded yet
    if (availableSetupDescriptions.value.length === 0) {
      await loadAvailableSetupDescriptions()
    }

    // Load position data
    await loadPositions()
    
    // Load statistics
    await loadStats()
    
    // Load detailed risk statistics
    await loadDetailedRiskStats()
  } catch (error) {
    console.error('Error loading data:', error)
  } finally {
    loading.value = false
  }
}

const loadPositions = async () => {
  try {
    const params = new URLSearchParams({
      symbol: filters.value.symbol
    })

    if (filters.value.setupDescription !== 'ALL') {
      params.append('setupDescription', filters.value.setupDescription)
    }

    if (filters.value.startDate) {
      params.append('startTs', new Date(filters.value.startDate).getTime().toString())
    }
    if (filters.value.endDate) {
      params.append('endTs', new Date(filters.value.endDate).getTime().toString())
    }

    const response = await fetch(`/api/position-history?${params}`)
    const result = await response.json()
    
    if (result.success) {
      positions.value = result.data
    }
  } catch (error) {
    console.error('Error loading positions:', error)
  }
}

const loadStats = async () => {
  try {
    const params = new URLSearchParams({
      symbol: filters.value.symbol
    })

    if (filters.value.setupDescription !== 'ALL') {
      params.append('setupDescription', filters.value.setupDescription)
    }

    if (filters.value.startDate) {
      params.append('startTs', new Date(filters.value.startDate).getTime().toString())
    }
    if (filters.value.endDate) {
      params.append('endTs', new Date(filters.value.endDate).getTime().toString())
    }

    const response = await fetch(`/api/position-history/stats?${params}`)
    const result = await response.json()
    
    if (result.success) {
      stats.value = result.data
    }
  } catch (error) {
    console.error('Error loading statistics:', error)
  }
}

const loadDetailedRiskStats = async () => {
  try {
    const params = new URLSearchParams({
      symbol: filters.value.symbol
    })

    if (filters.value.setupDescription !== 'ALL') {
      params.append('setupDescription', filters.value.setupDescription)
    }

    if (filters.value.startDate) {
      params.append('startTs', new Date(filters.value.startDate).getTime().toString())
    }
    if (filters.value.endDate) {
      params.append('endTs', new Date(filters.value.endDate).getTime().toString())
    }

    const response = await fetch(`/api/position-history/risk-stats?${params}`)
    const result = await response.json()
    
    if (result.success) {
      detailedStats.value = result.data
    }
  } catch (error) {
    console.error('Error loading detailed risk statistics:', error)
  }
}

const resetFilters = () => {
  filters.value = {
    symbol: 'ALL',
    setupDescription: 'ALL',
    startDate: '',
    endDate: ''
  }
  loadData()
}

const formatNumber = (value: number, maximum: number = 2): string => {
  return value.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: maximum
  })
}

const formatDate = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString('en-US')
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>

<style scoped>
/* Estilos customizados mínimos para complementar o Bootstrap */
.dashboard {
  background: transparent !important;
  min-height: 100vh;
}

.card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.table {
  color: inherit;
}

.table-dark {
  background: rgba(0, 0, 0, 0.3);
}

.table-hover tbody tr:hover {
  background: rgba(255, 255, 255, 0.05);
}

.form-control, .form-select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: inherit;
}

.form-control:focus, .form-select:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: #0d6efd;
  color: inherit;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-control::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* Animações suaves */
.btn {
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-1px);
}

/* Efeitos de glassmorphism */
.card-header {
  backdrop-filter: blur(10px);
  background: rgba(13, 110, 253, 0.8) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-footer {
  backdrop-filter: blur(10px);
  background: rgba(0, 0, 0, 0.3) !important;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Melhorias nos badges */
.badge {
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* Responsividade adicional */
@media (max-width: 768px) {
  .display-4 {
    font-size: 2rem;
  }
  
  .h3 {
    font-size: 1.5rem;
  }
  
  .card-body {
    padding: 1rem;
  }
}

@media (max-width: 576px) {
  .display-4 {
    font-size: 1.5rem;
  }
  
  .fs-1 {
    font-size: 2rem !important;
  }
}

/* Scrollbar customizada para dark mode */
.table-responsive::-webkit-scrollbar {
  height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Estilos específicos para o painel de custos */
.card-header.bg-warning {
  background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%) !important;
}

.border-end.border-secondary {
  border-color: rgba(255, 255, 255, 0.2) !important;
}

.table-sm td {
  padding: 0.5rem;
  vertical-align: middle;
}

/* Enhanced Header Styles */
.dashboard-header {
  background: linear-gradient(135deg, rgba(13, 110, 253, 0.1) 0%, rgba(79, 172, 254, 0.05) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1.5rem 0;
  margin-bottom: 2rem;
  backdrop-filter: blur(20px);
}

.breadcrumb {
  background: transparent;
  margin-bottom: 0;
  padding: 0;
}

.breadcrumb-item {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
}

.breadcrumb-item.active {
  color: #4facfe;
  font-weight: 500;
}

.breadcrumb-item + .breadcrumb-item::before {
  content: "›";
  color: rgba(255, 255, 255, 0.5);
  font-weight: bold;
}

.header-content {
  margin-top: 1rem;
}

.header-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  box-shadow: 0 8px 32px rgba(79, 172, 254, 0.3);
}

.header-icon i {
  font-size: 1.5rem;
  color: white;
}

.dashboard-title {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.dashboard-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
  font-weight: 400;
  margin-top: 0.25rem;
}

.header-actions .btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
}

.header-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Responsive Header */
@media (max-width: 768px) {
  .dashboard-header {
    padding: 1rem 0;
    margin-bottom: 1.5rem;
  }

  .header-icon {
    width: 50px;
    height: 50px;
    margin-right: 0.75rem;
  }

  .header-icon i {
    font-size: 1.25rem;
  }

  .dashboard-title {
    font-size: 2rem;
  }

  .dashboard-subtitle {
    font-size: 0.875rem;
  }

  .header-actions {
    margin-top: 1rem;
  }

  .header-actions .btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
  }
}

/* Enhanced Filters Section */
.filters-section {
  margin-bottom: 2rem;
}

.filters-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  overflow: hidden;
  transition: all 0.3s ease;
}

.filters-card:hover {
  border-color: rgba(79, 172, 254, 0.3);
  box-shadow: 0 8px 32px rgba(79, 172, 254, 0.1);
}

.filters-header {
  background: linear-gradient(135deg, rgba(79, 172, 254, 0.1) 0%, rgba(0, 242, 254, 0.05) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1rem 1.5rem;
}

.filter-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  box-shadow: 0 4px 16px rgba(79, 172, 254, 0.3);
}

.filter-icon i {
  font-size: 1rem;
  color: white;
}

.filters-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #4facfe;
}

.filters-subtitle {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.filters-body {
  padding: 1.5rem;
}

.filter-controls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.filter-label i {
  color: #4facfe;
}

.filter-select,
.filter-input {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  color: white;
  padding: 0.625rem 0.75rem;
  transition: all 0.3s ease;
}

.filter-select:focus,
.filter-input:focus {
  background: rgba(255, 255, 255, 0.12);
  border-color: #4facfe;
  box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
  color: white;
}

.filter-select option {
  background: #1a1f35;
  color: white;
}

.filter-actions {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: flex-end;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.action-btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 0.625rem 1rem;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* Responsive Filters */
@media (max-width: 992px) {
  .filter-controls-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-buttons {
    flex-direction: row;
    justify-content: stretch;
  }

  .action-btn {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .filters-header {
    padding: 0.75rem 1rem;
  }

  .filters-body {
    padding: 1rem;
  }

  .filter-controls-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .filter-icon {
    width: 35px;
    height: 35px;
    margin-right: 0.5rem;
  }

  .filters-title {
    font-size: 1rem;
  }

  .filters-subtitle {
    font-size: 0.8rem;
  }
}

/* Enhanced Statistics Cards */
.stats-section {
  margin-bottom: 3rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  position: relative;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(20px);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
}

.stat-card-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.stat-info {
  flex: 1;
}

.stat-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.25rem;
  line-height: 1.2;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

.stat-card-bg {
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  opacity: 0.1;
  transform: translate(30px, -30px);
  transition: all 0.3s ease;
}

.stat-card:hover .stat-card-bg {
  transform: translate(20px, -20px) scale(1.1);
  opacity: 0.15;
}

/* Card Variants */
.stat-card-primary .stat-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 8px 32px rgba(79, 172, 254, 0.3);
}

.stat-card-primary .stat-card-bg {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card-primary:hover {
  border-color: rgba(79, 172, 254, 0.3);
  box-shadow: 0 12px 40px rgba(79, 172, 254, 0.2);
}

.stat-card-success .stat-icon {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  box-shadow: 0 8px 32px rgba(34, 197, 94, 0.3);
}

.stat-card-success .stat-card-bg {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

.stat-card-success:hover {
  border-color: rgba(34, 197, 94, 0.3);
  box-shadow: 0 12px 40px rgba(34, 197, 94, 0.2);
}

.stat-card-danger .stat-icon {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 8px 32px rgba(239, 68, 68, 0.3);
}

.stat-card-danger .stat-card-bg {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.stat-card-danger:hover {
  border-color: rgba(239, 68, 68, 0.3);
  box-shadow: 0 12px 40px rgba(239, 68, 68, 0.2);
}

.stat-card-info .stat-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
}

.stat-card-info .stat-card-bg {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.stat-card-info:hover {
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 12px 40px rgba(59, 130, 246, 0.2);
}

.stat-card-warning .stat-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 0 8px 32px rgba(245, 158, 11, 0.3);
}

.stat-card-warning .stat-card-bg {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-card-warning:hover {
  border-color: rgba(245, 158, 11, 0.3);
  box-shadow: 0 12px 40px rgba(245, 158, 11, 0.2);
}

/* Responsive Stats */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .stat-card {
    padding: 1.25rem;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-card-content {
    gap: 0.75rem;
  }

  .stat-icon {
    width: 45px;
    height: 45px;
    font-size: 1.125rem;
  }

  .stat-value {
    font-size: 1.375rem;
  }

  .stat-label {
    font-size: 0.8rem;
  }

  .stat-trend {
    font-size: 0.7rem;
  }
}

/* Enhanced Analysis Section */
.analysis-section {
  margin-bottom: 3rem;
}

.section-header {
  text-align: center;
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.75rem;
  font-weight: 700;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.section-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
  margin-bottom: 0;
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.analysis-card {
  height: 100%;
}

.enhanced-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
}

.enhanced-card:hover {
  transform: translateY(-4px);
  border-color: rgba(245, 158, 11, 0.3);
  box-shadow: 0 12px 40px rgba(245, 158, 11, 0.2);
}

.enhanced-card-header {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.05) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1.25rem 1.5rem;
}

.card-header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.card-icon {
  width: 45px;
  height: 45px;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
  box-shadow: 0 6px 24px rgba(245, 158, 11, 0.3);
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #f59e0b;
  margin-bottom: 0.25rem;
}

.card-subtitle {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0;
}

.enhanced-card-body {
  padding: 1.5rem;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.metric-item {
  text-align: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.metric-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.metric-value {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.metric-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.trade-highlights {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.highlight-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.highlight-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.highlight-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.highlight-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.badge-success {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.badge-danger {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.badge-primary {
  background: rgba(79, 172, 254, 0.2);
  color: #4facfe;
  border: 1px solid rgba(79, 172, 254, 0.3);
}

.badge-secondary {
  background: rgba(156, 163, 175, 0.2);
  color: #9ca3af;
  border: 1px solid rgba(156, 163, 175, 0.3);
}

/* Responsive Analysis */
@media (max-width: 1200px) {
  .analysis-grid {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }
}

@media (max-width: 768px) {
  .section-title {
    font-size: 1.5rem;
  }

  .enhanced-card-header {
    padding: 1rem;
  }

  .enhanced-card-body {
    padding: 1rem;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .metric-item {
    padding: 0.75rem;
  }

  .metric-value {
    font-size: 1.125rem;
  }

  .highlight-item {
    padding: 0.625rem 0.75rem;
  }

  .card-icon {
    width: 40px;
    height: 40px;
    font-size: 1.125rem;
  }
}

/* Enhanced Charts Section */
.charts-section {
  margin-bottom: 3rem;
}

.chart-row {
  display: flex;
  justify-content: center;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.chart-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
}

.chart-card:hover {
  transform: translateY(-4px);
  border-color: rgba(79, 172, 254, 0.3);
  box-shadow: 0 12px 40px rgba(79, 172, 254, 0.15);
}

.chart-card-large {
  max-width: 1200px;
  width: 100%;
}

.chart-card-wide {
  grid-column: span 2;
}

.chart-header {
  background: linear-gradient(135deg, rgba(79, 172, 254, 0.1) 0%, rgba(0, 242, 254, 0.05) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1.25rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.chart-icon {
  width: 45px;
  height: 45px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.3);
}

.chart-icon-success {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

.chart-icon-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.chart-icon-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.chart-icon-primary {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.25rem;
}

.chart-subtitle {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0;
}

.chart-actions {
  display: flex;
  gap: 0.5rem;
}

.chart-actions .btn {
  border-radius: 8px;
  padding: 0.375rem 0.5rem;
  transition: all 0.3s ease;
}

.chart-actions .btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

.chart-body {
  padding: 1.5rem;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-card-large .chart-body {
  height: 450px;
}

/* Chart Container Improvements */
.chart-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart-container canvas {
  max-height: 100% !important;
  width: 100% !important;
}

/* Responsive Charts */
@media (max-width: 1400px) {
  .chart-card-wide {
    grid-column: span 1;
  }
}

@media (max-width: 992px) {
  .charts-grid {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }

  .chart-body {
    height: 350px;
    padding: 1.25rem;
  }

  .chart-card-large .chart-body {
    height: 400px;
  }
}

@media (max-width: 768px) {
  .chart-header {
    padding: 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .chart-actions {
    align-self: flex-end;
  }

  .chart-body {
    height: 300px;
    padding: 1rem;
  }

  .chart-card-large .chart-body {
    height: 350px;
  }

  .chart-icon {
    width: 40px;
    height: 40px;
    font-size: 1.125rem;
  }

  .chart-title {
    font-size: 1rem;
  }

  .chart-subtitle {
    font-size: 0.8rem;
  }
}

/* Enhanced Tables Section */
.tables-section {
  margin-bottom: 3rem;
}

.tables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.table-card {
  height: 100%;
}

/* Global Table Improvements */
.table {
  margin-bottom: 0;
}

.table th {
  background: rgba(255, 255, 255, 0.05) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 1rem 0.75rem;
}

.table td {
  border-color: rgba(255, 255, 255, 0.05) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  padding: 0.875rem 0.75rem;
  vertical-align: middle;
  font-size: 0.875rem;
}

.table-hover tbody tr:hover {
  background: rgba(79, 172, 254, 0.1) !important;
  transform: scale(1.01);
  transition: all 0.3s ease;
}

.table-responsive {
  border-radius: 0 0 16px 16px;
  overflow: hidden;
}

/* Enhanced Badge Styles */
.badge {
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  border: 1px solid transparent;
}

.badge.bg-secondary {
  background: rgba(156, 163, 175, 0.2) !important;
  color: #9ca3af !important;
  border-color: rgba(156, 163, 175, 0.3);
}

.badge.bg-primary {
  background: rgba(79, 172, 254, 0.2) !important;
  color: #4facfe !important;
  border-color: rgba(79, 172, 254, 0.3);
}

.badge.bg-success {
  background: rgba(34, 197, 94, 0.2) !important;
  color: #22c55e !important;
  border-color: rgba(34, 197, 94, 0.3);
}

.badge.bg-danger {
  background: rgba(239, 68, 68, 0.2) !important;
  color: #ef4444 !important;
  border-color: rgba(239, 68, 68, 0.3);
}

.badge.bg-warning {
  background: rgba(245, 158, 11, 0.2) !important;
  color: #f59e0b !important;
  border-color: rgba(245, 158, 11, 0.3);
}

.badge.bg-info {
  background: rgba(59, 130, 246, 0.2) !important;
  color: #3b82f6 !important;
  border-color: rgba(59, 130, 246, 0.3);
}

.badge.bg-purple {
  background: rgba(147, 51, 234, 0.2) !important;
  color: #9333ea !important;
  border-color: rgba(147, 51, 234, 0.3);
}

/* Text Color Improvements */
.text-success {
  color: #22c55e !important;
}

.text-danger {
  color: #ef4444 !important;
}

.text-warning {
  color: #f59e0b !important;
}

.text-info {
  color: #3b82f6 !important;
}

.text-primary {
  color: #4facfe !important;
}

/* Responsive Tables */
@media (max-width: 1200px) {
  .tables-grid {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }
}

@media (max-width: 768px) {
  .table th,
  .table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.8rem;
  }

  .table th {
    font-size: 0.75rem;
  }

  .badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.7rem;
  }
}

/* Advanced Responsiveness */
@media (max-width: 1400px) {
  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .analysis-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 1200px) {
  .dashboard-header {
    padding: 1.25rem 0;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }

  .filter-controls-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 992px) {
  .dashboard-header {
    padding: 1rem 0;
  }

  .header-content .row {
    flex-direction: column;
    text-align: center;
  }

  .header-actions {
    margin-top: 1rem;
    text-align: center !important;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .filters-body {
    padding: 1.25rem;
  }

  .filter-controls-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .action-buttons {
    flex-direction: row;
    gap: 0.75rem;
  }

  .action-btn {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 0 0.5rem;
  }

  .dashboard-header {
    margin-bottom: 1.5rem;
  }

  .header-icon {
    width: 45px;
    height: 45px;
    margin-right: 0.5rem;
  }

  .dashboard-title {
    font-size: 1.75rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .filters-header {
    padding: 0.75rem 1rem;
  }

  .filters-body {
    padding: 1rem;
  }

  .section-header {
    margin-bottom: 1.5rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .container-fluid {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
}

@media (max-width: 576px) {
  .dashboard {
    padding: 0 0.25rem;
  }

  .dashboard-title {
    font-size: 1.5rem;
  }

  .dashboard-subtitle {
    font-size: 0.875rem;
  }

  .header-actions .btn {
    font-size: 0.8rem;
    padding: 0.375rem 0.625rem;
  }

  .stat-card {
    padding: 0.875rem;
  }

  .stat-card-content {
    gap: 0.5rem;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .stat-value {
    font-size: 1.25rem;
  }

  .filters-header {
    padding: 0.625rem 0.75rem;
  }

  .filters-body {
    padding: 0.75rem;
  }

  .filter-icon {
    width: 32px;
    height: 32px;
    margin-right: 0.5rem;
  }

  .filters-title {
    font-size: 0.95rem;
  }

  .section-title {
    font-size: 1.25rem;
  }

  .section-subtitle {
    font-size: 0.875rem;
  }

  .container-fluid {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}

/* Enhanced Loading State */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 2rem;
}

.loading-content {
  text-align: center;
  max-width: 400px;
}

.loading-spinner {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 2rem;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spin 2s linear infinite;
}

.spinner-ring:nth-child(1) {
  border-top-color: #4facfe;
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  border-top-color: #00f2fe;
  animation-delay: 0.3s;
  width: 70%;
  height: 70%;
  top: 15%;
  left: 15%;
}

.spinner-ring:nth-child(3) {
  border-top-color: #22c55e;
  animation-delay: 0.6s;
  width: 40%;
  height: 40%;
  top: 30%;
  left: 30%;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-title {
  font-size: 1.5rem;
  font-weight: 600;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.loading-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
  margin-bottom: 2rem;
}

.loading-progress {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 50%, #22c55e 100%);
  border-radius: 2px;
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% { transform: translateX(-100%); }
  50% { transform: translateX(0%); }
  100% { transform: translateX(100%); }
}

/* Enhanced Transitions and Animations */
* {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dashboard {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered Animation for Cards */
.stat-card {
  animation: slideInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }
.stat-card:nth-child(5) { animation-delay: 0.5s; }
.stat-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Hover Effects */
.btn {
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus States */
.form-control:focus,
.form-select:focus,
.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.3);
}

/* Loading Animation for Charts */
.chart-body {
  position: relative;
}

.chart-body::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(79, 172, 254, 0.1) 50%, transparent 100%);
  animation: shimmer 2s infinite;
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.chart-body.loading::before {
  opacity: 1;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print Styles */
@media print {
  .dashboard-header,
  .filters-section,
  .btn {
    display: none !important;
  }

  .chart-body {
    height: auto !important;
  }

  .stat-card,
  .enhanced-card,
  .chart-card {
    break-inside: avoid;
    margin-bottom: 1rem;
  }
}

.table-borderless td {
  border: none;
}

/* Melhorias para os cards de estatísticas menores */
@media (max-width: 1200px) {
  .col-lg-2 .card-title {
    font-size: 1.2rem;
  }
  
  .col-lg-2 .fs-1 {
    font-size: 1.5rem !important;
  }
}

@media (max-width: 768px) {
  .col-lg-2 .card-title {
    font-size: 1rem;
  }
  
  .col-lg-2 .fs-1 {
    font-size: 1.2rem !important;
  }
}

/* Estilos para os gráficos */
.card-body canvas {
  max-height: 300px;
}

/* Melhorias para responsividade dos gráficos */
@media (max-width: 992px) {
  .chart-container {
    height: 250px !important;
  }
}

@media (max-width: 768px) {
  .chart-container {
    height: 200px !important;
  }
}
</style> 