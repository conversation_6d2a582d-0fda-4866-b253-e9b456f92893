@import url('https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-white dark:bg-gray-900 text-gray-900 dark:text-white;
  }
}

/* Global Variables */
:root {
  --bs-body-bg: #0a0e1a;
  --bs-body-color: #ffffff;
  --primary-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --success-color: #22c55e;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;
}

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  background: linear-gradient(135deg, #0a0e1a 0%, #1a1f35 50%, #2a2f45 100%);
  min-height: 100vh;
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  min-height: 100vh;
}

/* Smooth transitions for all elements */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, transform 0.3s ease;
}

/* Form Elements */
.form-control, .form-select {
  background-color: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
  border-radius: 8px !important;
  backdrop-filter: blur(10px);
}

.form-control:focus, .form-select:focus {
  background-color: rgba(255, 255, 255, 0.08) !important;
  border-color: rgba(79, 172, 254, 0.4) !important;
  color: #ffffff !important;
  box-shadow: 0 0 0 0.25rem rgba(79, 172, 254, 0.15) !important;
}

.form-control::placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
}

/* Card Components */
.card {
  background-color: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px);
  border-radius: 16px !important;
}

.card-header {
  background-color: rgba(0, 0, 0, 0.2) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Table Styles */
.table {
  color: #ffffff !important;
  --bs-table-bg: transparent;
}

.table-dark {
  background-color: rgba(0, 0, 0, 0.2) !important;
  color: #ffffff !important;
}

.table-hover tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05) !important;
  color: #ffffff !important;
}

.table th {
  border-color: rgba(255, 255, 255, 0.1) !important;
  background-color: rgba(0, 0, 0, 0.3) !important;
}

.table td {
  border-color: rgba(255, 255, 255, 0.05) !important;
}

/* Button Styles */
.btn {
  border-radius: 8px !important;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background: var(--primary-gradient) !important;
  border: none !important;
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
}

.btn-success {
  background: rgba(34, 197, 94, 0.9) !important;
  border-color: rgba(34, 197, 94, 0.3) !important;
}

.btn-danger {
  background: rgba(239, 68, 68, 0.9) !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
}

.btn-warning {
  background: rgba(245, 158, 11, 0.9) !important;
  border-color: rgba(245, 158, 11, 0.3) !important;
}

.btn-info {
  background: rgba(59, 130, 246, 0.9) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
}

.btn-outline-primary {
  color: #4facfe !important;
  border-color: rgba(79, 172, 254, 0.4) !important;
}

.btn-outline-primary:hover {
  background-color: rgba(79, 172, 254, 0.1) !important;
  border-color: rgba(79, 172, 254, 0.6) !important;
  color: #4facfe !important;
}

/* Badge Styles */
.badge {
  font-weight: 600;
  border-radius: 8px !important;
  letter-spacing: 0.3px;
}

.bg-success {
  background-color: rgba(34, 197, 94, 0.9) !important;
}

.bg-danger {
  background-color: rgba(239, 68, 68, 0.9) !important;
}

.bg-warning {
  background-color: rgba(245, 158, 11, 0.9) !important;
}

.bg-info {
  background-color: rgba(59, 130, 246, 0.9) !important;
}

.bg-secondary {
  background-color: rgba(107, 114, 128, 0.9) !important;
}

/* Alert Styles */
.alert {
  border-radius: 12px !important;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.alert-success {
  background-color: rgba(34, 197, 94, 0.1) !important;
  border-color: rgba(34, 197, 94, 0.2) !important;
  color: #22c55e !important;
}

.alert-danger {
  background-color: rgba(239, 68, 68, 0.1) !important;
  border-color: rgba(239, 68, 68, 0.2) !important;
  color: #ef4444 !important;
}

.alert-warning {
  background-color: rgba(245, 158, 11, 0.1) !important;
  border-color: rgba(245, 158, 11, 0.2) !important;
  color: #f59e0b !important;
}

.alert-info {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-color: rgba(59, 130, 246, 0.2) !important;
  color: #3b82f6 !important;
}

/* Modal Styles */
.modal-content {
  background-color: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px);
  border-radius: 16px !important;
}

.modal-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.modal-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Dropdown Styles */
.dropdown-menu {
  background-color: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px);
  border-radius: 12px !important;
}

.dropdown-item {
  color: rgba(255, 255, 255, 0.8) !important;
}

.dropdown-item:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
}

/* Scrollbar Customization */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: rgba(79, 172, 254, 0.3);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(79, 172, 254, 0.5);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(79, 172, 254, 0.3) rgba(255, 255, 255, 0.05);
}

/* Loading Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Utility Classes */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

.slide-in {
  animation: slideIn 0.5s ease-out;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
}

.text-gradient {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Focus States */
*:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.3) !important;
}

/* Selection Colors */
::selection {
  background-color: rgba(79, 172, 254, 0.3);
  color: #ffffff;
}

::-moz-selection {
  background-color: rgba(79, 172, 254, 0.3);
  color: #ffffff;
}

/* Dark Theme Specific Overrides */
[data-bs-theme="dark"] {
  --bs-body-bg: #0a0e1a;
  --bs-body-color: #ffffff;
  --bs-border-color: rgba(255, 255, 255, 0.1);
  --bs-secondary-bg: rgba(255, 255, 255, 0.05);
  --bs-tertiary-bg: rgba(255, 255, 255, 0.03);
}

/* Print Styles */
@media print {
  body {
    background: white !important;
    color: black !important;
  }
  
  .glass-effect {
    background: white !important;
    border: 1px solid #ccc !important;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
} 